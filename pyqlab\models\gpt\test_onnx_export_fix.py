"""
测试ONNX导出修复是否有效
"""

import torch
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bar_gpt4 import BarGpt4


def test_onnx_export():
    """测试ONNX导出功能"""
    print("=== 测试ONNX导出修复 ===\n")
    
    # 创建测试模型
    model = BarGpt4(
        block_size=30,
        code_size=100,
        vocab_size=90,  # 使用较小的词汇表进行测试
        n_layer=2,      # 使用较少的层进行测试
        n_head=4,
        d_model=64,     # 使用较小的模型维度
        time_encoding='timeF',
        time_embed_type='time_feature',
        freq='t',       # 这是导致问题的关键参数
        pos_embed_type='rope',
        dropout=0.1
    )
    
    print(f"模型参数数量: {model.get_num_params():,}")
    print(f"模型配置:")
    print(f"  - freq: {model.freq}")
    print(f"  - time_encoding: {model.time_encoding}")
    print(f"  - time_embed_type: {model.time_embed_type}")
    
    # 切换到推理模式
    model.inference_mode()
    
    # 测试前向传播
    print(f"\n=== 测试前向传播 ===")
    batch_size = 1
    seq_len = 10
    
    # 创建测试输入
    code = torch.randint(0, 100, (batch_size, seq_len))
    x = torch.randint(0, 90, (batch_size, seq_len))
    x_mark = torch.randn(batch_size, seq_len, 5)  # freq='t'需要5个时间特征
    
    print(f"输入形状:")
    print(f"  - code: {code.shape}")
    print(f"  - x: {x.shape}")
    print(f"  - x_mark: {x_mark.shape}")
    
    try:
        # 测试前向传播
        with torch.no_grad():
            logits, _ = model(code, x, x_mark)
        print(f"✅ 前向传播成功")
        print(f"  - 输出形状: {logits.shape}")
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        return False
    
    # 测试ONNX导出
    print(f"\n=== 测试ONNX导出 ===")
    onnx_path = "test_model.onnx"
    
    try:
        success = model.export_onnx(
            save_path=onnx_path,
            batch_size=1,
            seq_len=10,
            dynamic_axes=True,
            opset_version=13
        )
        
        if success:
            print(f"✅ ONNX导出成功")
            
            # 检查文件是否存在
            if os.path.exists(onnx_path):
                file_size = os.path.getsize(onnx_path) / (1024 * 1024)
                print(f"  - 文件大小: {file_size:.2f} MB")
                
                # 测试ONNX推理
                print(f"\n=== 测试ONNX推理 ===")
                try:
                    onnx_output = model.inference_with_onnx(onnx_path, code, x, x_mark)
                    print(f"✅ ONNX推理成功")
                    print(f"  - ONNX输出形状: {onnx_output.shape}")
                    
                    # 比较PyTorch和ONNX输出
                    diff = torch.abs(logits - onnx_output).max().item()
                    print(f"  - 最大差异: {diff:.6f}")
                    
                    if diff < 1e-4:
                        print(f"✅ PyTorch和ONNX输出一致")
                    else:
                        print(f"⚠️  PyTorch和ONNX输出存在差异")
                        
                except Exception as e:
                    print(f"❌ ONNX推理失败: {e}")
                
                # 清理测试文件
                try:
                    os.remove(onnx_path)
                    print(f"  - 清理测试文件: {onnx_path}")
                except:
                    pass
                    
            else:
                print(f"❌ ONNX文件未生成")
                return False
        else:
            print(f"❌ ONNX导出失败")
            return False
            
    except Exception as e:
        print(f"❌ ONNX导出过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_different_freq():
    """测试不同freq参数的ONNX导出"""
    print(f"\n=== 测试不同freq参数 ===")
    
    freq_configs = [
        ('t', 5),   # minutely - 5个特征
        ('h', 4),   # hourly - 4个特征
        ('d', 3),   # daily - 3个特征
    ]
    
    for freq, expected_dim in freq_configs:
        print(f"\n--- 测试 freq='{freq}' (期望维度: {expected_dim}) ---")
        
        try:
            model = BarGpt4(
                block_size=10,
                code_size=50,
                vocab_size=50,
                n_layer=1,
                n_head=2,
                d_model=32,
                time_encoding='timeF',
                time_embed_type='time_feature',
                freq=freq,
                pos_embed_type='rope',
                dropout=0.1
            )
            
            model.inference_mode()
            
            # 创建正确维度的输入
            batch_size = 1
            seq_len = 5
            code = torch.randint(0, 50, (batch_size, seq_len))
            x = torch.randint(0, 50, (batch_size, seq_len))
            x_mark = torch.randn(batch_size, seq_len, expected_dim)
            
            # 测试前向传播
            with torch.no_grad():
                logits, _ = model(code, x, x_mark)
            
            # 测试ONNX导出
            onnx_path = f"test_model_{freq}.onnx"
            success = model.export_onnx(
                save_path=onnx_path,
                batch_size=1,
                seq_len=5,
                dynamic_axes=False,
                opset_version=13
            )
            
            if success:
                print(f"✅ freq='{freq}' 导出成功")
                # 清理文件
                try:
                    os.remove(onnx_path)
                except:
                    pass
            else:
                print(f"❌ freq='{freq}' 导出失败")
                
        except Exception as e:
            print(f"❌ freq='{freq}' 测试失败: {e}")


if __name__ == '__main__':
    print("开始测试ONNX导出修复...")
    
    # 测试主要功能
    success = test_onnx_export()
    
    # 测试不同freq参数
    test_different_freq()
    
    # 总结
    print(f"\n=== 测试总结 ===")
    if success:
        print(f"🎉 ONNX导出修复成功！")
        print(f"主要修复内容:")
        print(f"  1. 修正了x_mark输入维度的计算")
        print(f"  2. 根据freq参数正确设置时间特征维度")
        print(f"  3. freq='t'使用5个时间特征，其他频率使用3个")
    else:
        print(f"❌ ONNX导出仍存在问题，需要进一步调试")
