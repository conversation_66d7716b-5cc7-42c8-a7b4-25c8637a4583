# BarGpt4模型ONNX导出功能

本文档介绍如何使用新增的ONNX导出功能来训练和导出BarGpt4模型。

## 功能概述

新增的ONNX导出功能包括：

1. **训练时自动导出**: 在交叉验证训练完成后，自动选择最佳模型并导出为ONNX格式
2. **配置文件保存**: 同时保存模型配置信息，便于后续使用
3. **模型验证**: 导出后自动验证ONNX模型的正确性
4. **推理测试**: 提供完整的ONNX模型推理示例

## 使用方法

### 1. 训练并导出模型

使用 `--export_onnx` 参数启用ONNX导出功能：

```bash
python pyqlab/models/gpt/train_bar_gpt4_with_tokenizer.py \
    --data_file "f:/hqdata/min1/fut_min1_2025.parquet" \
    --block_size 30 \
    --n_layer 4 \
    --n_head 8 \
    --d_model 128 \
    --mapping_strategy quantile \
    --n_bins 100 \
    --max_epochs 10 \
    --k_folds 3 \
    --export_onnx
```

或者使用提供的批处理脚本：

```bash
train_and_export_onnx.bat
```

### 2. 输出文件

训练完成后，会在日志目录下生成以下文件：

```
lightning_logs_tokenized/
├── onnx_models/
│   ├── BarGpt4_Tokenized_quantile_100bins_20241201_143022.onnx  # ONNX模型文件
│   └── BarGpt4_Tokenized_quantile_100bins_20241201_143022_config.json  # 配置文件
└── tokenizer_quantile_100.pkl  # Tokenizer文件
```

### 3. 配置文件内容

配置文件包含完整的模型和训练信息：

```json
{
  "model_name": "BarGpt4_Tokenized_quantile_100bins_20241201_143022",
  "export_time": "20241201_143022",
  "best_fold": 0,
  "best_score": 3.7925,
  "model_args": {
    "block_size": 30,
    "code_size": 100,
    "vocab_size": 500,
    "n_layer": 4,
    "n_head": 8,
    "d_model": 128,
    "time_encoding": "timeF",
    "pos_embed_type": "rope"
  },
  "tokenizer_config": {
    "mapping_strategy": "quantile",
    "balancing_strategy": "frequency",
    "n_bins": 100,
    "features": ["change", "body", "upper_shadow", "lower_shadow", "volume_ratio"],
    "combination_method": "hierarchical"
  },
  "dataset_info": {
    "vocab_size": 500,
    "code_size": 100,
    "sample_count": 50000
  }
}
```

### 4. 使用ONNX模型进行推理

使用提供的推理脚本：

```bash
python pyqlab/models/gpt/examples/use_onnx_model.py \
    --onnx_path "lightning_logs_tokenized/onnx_models/BarGpt4_Tokenized_quantile_100bins_20241201_143022.onnx" \
    --config_path "lightning_logs_tokenized/onnx_models/BarGpt4_Tokenized_quantile_100bins_20241201_143022_config.json" \
    --tokenizer_path "tokenizer_quantile_100.pkl" \
    --top_k 5
```

### 5. 测试ONNX导出功能

使用测试脚本验证已有模型的ONNX导出：

```bash
python pyqlab/models/gpt/test_onnx_export.py \
    --model_path "path/to/your/model.ckpt" \
    --output_dir "./onnx_test"
```

## 代码示例

### Python中使用ONNX模型

```python
import numpy as np
import onnxruntime as ort

# 加载ONNX模型
session = ort.InferenceSession("model.onnx")

# 准备输入数据
batch_size, seq_len = 1, 30
code = np.random.randint(0, 100, (batch_size, seq_len), dtype=np.int64)
x = np.random.randint(0, 500, (batch_size, seq_len), dtype=np.int64)
x_mark = np.random.randn(batch_size, seq_len, 8).astype(np.float32)

# 进行推理
ort_inputs = {
    'code': code,
    'x': x,
    'x_mark': x_mark
}
ort_outputs = session.run(None, ort_inputs)
predictions = ort_outputs[0]

print(f"预测结果形状: {predictions.shape}")
```

## 技术细节

### ONNX导出特性

- **动态轴支持**: 支持可变长度的输入序列
- **推理优化**: 模型自动切换到推理模式，移除训练相关的计算
- **兼容性**: 使用ONNX opset version 13，确保广泛兼容性
- **验证机制**: 导出后自动验证模型正确性

### 输入输出格式

**输入**:
- `code`: 证券代码，形状为 `(batch_size, seq_len)`，数据类型 `int64`
- `x`: token序列，形状为 `(batch_size, seq_len)`，数据类型 `int64`
- `x_mark`: 时间特征，形状为 `(batch_size, seq_len, time_dim)`，数据类型 `float32`

**输出**:
- `output`: 预测logits，形状为 `(batch_size, 1, vocab_size)`，数据类型 `float32`

### 性能优化建议

1. **批处理大小**: 推理时建议使用 `batch_size=1` 以获得最佳性能
2. **序列长度**: 使用训练时的 `block_size` 作为输入序列长度
3. **数据类型**: 确保输入数据类型与模型期望一致
4. **内存管理**: 对于大量推理任务，考虑使用ONNX Runtime的内存优化选项

## 依赖要求

```bash
pip install onnx onnxruntime torch pytorch-lightning
```

## 故障排除

### 常见问题

1. **导出失败**: 检查模型是否正确加载，确保所有依赖已安装
2. **推理错误**: 验证输入数据的形状和数据类型
3. **性能问题**: 考虑使用ONNX Runtime的GPU版本或优化选项

### 调试建议

1. 使用 `test_onnx_export.py` 脚本测试导出功能
2. 检查ONNX模型的输入输出规格
3. 比较原始模型和ONNX模型的输出差异

## 更新日志

- **2024-12-01**: 添加ONNX导出功能到训练脚本
- **2024-12-01**: 创建推理示例和测试脚本
- **2024-12-01**: 添加配置文件保存和模型验证功能
