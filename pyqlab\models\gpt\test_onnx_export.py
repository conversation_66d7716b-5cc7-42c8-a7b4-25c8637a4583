"""
测试BarGpt4模型ONNX导出功能

这个脚本用于测试训练好的BarGpt4模型是否能够成功导出为ONNX格式，
并验证导出的模型是否能够正常进行推理。
"""

import os
import sys
import torch
import numpy as np
import json
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def test_onnx_export(model_path, output_dir="./onnx_test"):
    """测试ONNX导出功能"""
    print("=== 测试BarGpt4模型ONNX导出功能 ===\n")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 加载模型检查点
        print(f"正在加载模型: {model_path}")
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # 提取模型参数
        if 'hyper_parameters' in checkpoint:
            # Lightning模型格式
            hparams = checkpoint['hyper_parameters']
            state_dict = checkpoint['state_dict']
            
            # 移除Lightning包装器的前缀
            model_state_dict = {}
            for key, value in state_dict.items():
                if key.startswith('model.'):
                    model_state_dict[key[6:]] = value  # 移除'model.'前缀
                else:
                    model_state_dict[key] = value
        else:
            # 普通PyTorch模型格式
            hparams = checkpoint.get('config', {})
            model_state_dict = checkpoint.get('model_state_dict', checkpoint)
        
        print(f"模型超参数: {hparams}")
        
        # 创建模型实例
        model = BarGpt4(
            block_size=hparams.get('block_size', 30),
            code_size=hparams.get('code_size', 100),
            vocab_size=hparams.get('vocab_size', 500),
            n_layer=hparams.get('n_layer', 4),
            n_head=hparams.get('n_head', 8),
            d_model=hparams.get('d_model', 128),
            time_encoding=hparams.get('time_encoding', 'timeF'),
            time_embed_type=hparams.get('time_embed_type', 'time_feature'),
            freq=hparams.get('freq', 't'),
            pos_embed_type=hparams.get('pos_embed_type', 'rope'),
            dropout=hparams.get('dropout', 0.1)
        )
        
        # 加载权重
        model.load_state_dict(model_state_dict)
        model.eval()
        
        print(f"模型加载成功，参数数量: {model.get_num_params():,}")
        
        # 切换到推理模式
        model.inference_mode()
        
        # 导出ONNX模型
        onnx_path = os.path.join(output_dir, "test_bar_gpt4.onnx")
        print(f"\n正在导出ONNX模型到: {onnx_path}")
        
        success = model.export_onnx(
            save_path=onnx_path,
            batch_size=1,
            seq_len=hparams.get('block_size', 30),
            dynamic_axes=True,
            opset_version=13
        )
        
        if success:
            print("✅ ONNX导出成功！")
            
            # 保存模型配置
            config_path = os.path.join(output_dir, "test_bar_gpt4_config.json")
            config = {
                'model_type': 'BarGpt4',
                'hyperparameters': hparams,
                'onnx_path': onnx_path,
                'export_success': True
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"配置文件已保存: {config_path}")
            
            # 测试ONNX推理
            test_onnx_inference(model, onnx_path, hparams)
            
        else:
            print("❌ ONNX导出失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_onnx_inference(original_model, onnx_path, hparams):
    """测试ONNX模型推理"""
    print(f"\n=== 测试ONNX模型推理 ===")
    
    try:
        import onnxruntime as ort
    except ImportError:
        print("⚠️  未安装onnxruntime，跳过推理测试")
        print("请运行: pip install onnxruntime")
        return
    
    try:
        # 创建测试输入
        batch_size = 1
        seq_len = hparams.get('block_size', 30)
        code_size = hparams.get('code_size', 100)
        
        # 生成随机测试数据
        test_code = torch.randint(0, code_size, (batch_size, seq_len), dtype=torch.long)
        test_x = torch.randint(0, hparams.get('vocab_size', 500), (batch_size, seq_len), dtype=torch.long)
        
        # 时间特征维度
        time_dim = 8 if hparams.get('time_encoding') == 'timeF' else 4
        test_x_mark = torch.randn(batch_size, seq_len, time_dim, dtype=torch.float32)
        
        print(f"测试输入形状:")
        print(f"  code: {test_code.shape}")
        print(f"  x: {test_x.shape}")
        print(f"  x_mark: {test_x_mark.shape}")
        
        # 原始模型推理
        with torch.no_grad():
            original_output, _ = original_model(test_code, test_x, test_x_mark)
        
        print(f"原始模型输出形状: {original_output.shape}")
        
        # ONNX模型推理
        session = ort.InferenceSession(onnx_path)
        
        ort_inputs = {
            'code': test_code.numpy(),
            'x': test_x.numpy(),
            'x_mark': test_x_mark.numpy()
        }
        
        ort_outputs = session.run(None, ort_inputs)
        onnx_output = torch.tensor(ort_outputs[0])
        
        print(f"ONNX模型输出形状: {onnx_output.shape}")
        
        # 比较输出
        diff = torch.abs(original_output - onnx_output).max().item()
        print(f"输出差异 (最大绝对误差): {diff:.6f}")
        
        if diff < 1e-4:
            print("✅ ONNX模型推理测试通过！")
        else:
            print(f"⚠️  输出差异较大: {diff:.6f}")
            
    except Exception as e:
        print(f"❌ ONNX推理测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    parser = ArgumentParser(description='测试BarGpt4模型ONNX导出功能')
    parser.add_argument('--model_path', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--output_dir', type=str, default='./onnx_test', help='输出目录')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model_path):
        print(f"❌ 模型文件不存在: {args.model_path}")
        return
    
    success = test_onnx_export(args.model_path, args.output_dir)
    
    if success:
        print(f"\n✅ 测试完成！输出文件保存在: {args.output_dir}")
    else:
        print(f"\n❌ 测试失败！")


if __name__ == '__main__':
    main()
