@echo off
REM Train BarGpt4 model with high-quality tokens from BarTokenizer
REM Test if it can solve prediction concentration caused by sample imbalance

e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt

echo "=== Training BarGpt4 with BarTokenizer ==="
echo "Goal: Solve prediction concentration caused by sample imbalance"
echo "Data: min1 period data"
echo ""

REM Basic configuration - using quantile mapping strategy
python train_bar_gpt4_with_tokenizer.py ^
--log_dir e:/lab/RoboQuant/pylab/models/lightning_logs_tokenized ^
--data_file f:/hqdata/fut_top_min1.parquet ^
--data_path f:/hqdata ^
--block_size 30 ^
--mapping_strategy quantile ^
--balancing_strategy none ^
--max_token_frequency 0.08 ^
--gini_threshold 0.6 ^
--start_year 2025 ^
--end_year 2025 ^
--n_bins 30 ^
--features body,upper_shadow,lower_shadow ^
--combination_method hierarchical ^
--n_layer 4 ^
--n_head 8 ^
--d_model 96 ^
--time_encoding timeF ^
--time_embed_type time_feature ^
--pos_embed_type rope ^
--dropout 0.1 ^
--batch_size 64 ^
--lr 1e-4 ^
--weight_decay 0.01 ^
--max_epochs 3 ^
--k_folds 3 ^
--early_stop 5 ^
--min_delta 1 ^
--num_workers 0 ^
--seed 42 ^
--use_class_weights ^
--export_onnx

echo ""
echo "=== First round training completed ==="
echo ""

REM Comparison experiment - using adaptive mapping strategy
echo "=== Starting comparison experiment: adaptive mapping strategy ==="

@REM python train_bar_gpt4_with_tokenizer.py ^
@REM --data_file f:/hqdata/fut_top_min1.parquet ^
@REM --data_path f:/hqdata ^
@REM --block_size 30 ^
@REM --mapping_strategy adaptive ^
@REM --balancing_strategy frequency ^
@REM --n_bins 100 ^
@REM --max_token_frequency 0.08 ^
@REM --gini_threshold 0.6 ^
@REM --n_layer 4 ^
@REM --n_head 8 ^
@REM --d_model 128 ^
@REM --time_encoding timeF ^
@REM --time_embed_type time_feature ^
@REM --pos_embed_type rope ^
@REM --dropout 0.1 ^
@REM --batch_size 64 ^
@REM --lr 1e-4 ^
@REM --weight_decay 0.01 ^
@REM --max_epochs 3 ^
@REM --k_folds 3 ^
@REM --early_stop 5 ^
@REM --min_delta 1e-3 ^
@REM --num_workers 0 ^
@REM --seed 42 ^
@REM --log_dir lightning_logs_tokenized_adaptive ^
@REM --use_class_weights

echo ""
echo "=== Second round training completed ==="
echo ""

REM Comparison experiment - without balancing strategy
echo "=== Starting comparison experiment: without balancing strategy ==="

@REM python train_bar_gpt4_with_tokenizer.py ^
@REM --data_file f:/hqdata/fut_top_min1.parquet ^
@REM --data_path f:/hqdata ^
@REM --block_size 30 ^
@REM --mapping_strategy quantile ^
@REM --balancing_strategy none ^
@REM --n_bins 100 ^
@REM --max_token_frequency 0.2 ^
@REM --gini_threshold 0.9 ^
@REM --n_layer 4 ^
@REM --n_head 8 ^
@REM --d_model 128 ^
@REM --time_encoding timeF ^
@REM --time_embed_type time_feature ^
@REM --pos_embed_type rope ^
@REM --dropout 0.1 ^
@REM --batch_size 64 ^
@REM --lr 1e-4 ^
@REM --weight_decay 0.01 ^
@REM --max_epochs 3 ^
@REM --k_folds 3 ^
@REM --early_stop 5 ^
@REM --min_delta 1e-3 ^
@REM --num_workers 0 ^
@REM --seed 42 ^
@REM --log_dir lightning_logs_tokenized_no_balance

echo ""
echo "=== All training completed ==="
echo ""
echo "Please check the results in the following log directories:"
echo "1. lightning_logs_tokenized - quantile + frequency balancing"
echo "2. lightning_logs_tokenized_adaptive - adaptive + frequency balancing"
echo "3. lightning_logs_tokenized_no_balance - quantile + no balancing"
echo ""
echo "Focus on prediction diversity and max prediction frequency metrics"

@REM pause
