"""
使用导出的ONNX格式BarGpt4模型进行推理

这个脚本演示如何加载和使用导出的ONNX格式模型进行预测。
"""

import os
import sys
import json
import numpy as np
import pandas as pd
import pickle
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import onnxruntime as ort
except ImportError:
    print("请安装onnxruntime: pip install onnxruntime")
    sys.exit(1)


class ONNXBarGpt4Predictor:
    """ONNX格式BarGpt4模型预测器"""
    
    def __init__(self, onnx_path, config_path, tokenizer_path=None):
        """
        初始化预测器
        
        参数:
        - onnx_path: ONNX模型文件路径
        - config_path: 模型配置文件路径
        - tokenizer_path: Tokenizer文件路径（可选）
        """
        self.onnx_path = onnx_path
        self.config_path = config_path
        self.tokenizer_path = tokenizer_path
        
        # 加载配置
        self.load_config()
        
        # 创建ONNX运行时会话
        self.session = ort.InferenceSession(onnx_path)
        
        # 加载tokenizer（如果提供）
        self.tokenizer = None
        if tokenizer_path and os.path.exists(tokenizer_path):
            self.load_tokenizer()
        
        print(f"ONNX模型加载成功: {onnx_path}")
        print(f"模型配置: {self.model_config['model_name']}")
    
    def load_config(self):
        """加载模型配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.model_config = json.load(f)
        
        self.model_args = self.model_config['model_args']
        self.tokenizer_config = self.model_config.get('tokenizer_config', {})
        self.dataset_info = self.model_config.get('dataset_info', {})
    
    def load_tokenizer(self):
        """加载tokenizer"""
        try:
            with open(self.tokenizer_path, 'rb') as f:
                self.tokenizer = pickle.load(f)
            print(f"Tokenizer加载成功: {self.tokenizer_path}")
        except Exception as e:
            print(f"Tokenizer加载失败: {e}")
            self.tokenizer = None
    
    def predict(self, code, x, x_mark):
        """
        使用ONNX模型进行预测
        
        参数:
        - code: 代码输入，形状为(batch_size, seq_len)
        - x: 序列输入，形状为(batch_size, seq_len)
        - x_mark: 时间标记输入，形状为(batch_size, seq_len, mark_dim)
        
        返回:
        - 预测结果
        """
        # 准备输入
        ort_inputs = {
            'code': code.astype(np.int64),
            'x': x.astype(np.int64),
            'x_mark': x_mark.astype(np.float32)
        }
        
        # 运行推理
        ort_outputs = self.session.run(None, ort_inputs)
        
        return ort_outputs[0]
    
    def predict_next_token(self, code, x, x_mark, top_k=5):
        """
        预测下一个token
        
        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - top_k: 返回概率最高的k个token
        
        返回:
        - top_k个token及其概率
        """
        # 获取预测logits
        logits = self.predict(code, x, x_mark)
        
        # 应用softmax获取概率
        probs = self.softmax(logits[0, -1, :])  # 取最后一个时间步
        
        # 获取top-k结果
        top_k_indices = np.argsort(probs)[-top_k:][::-1]
        top_k_probs = probs[top_k_indices]
        
        return top_k_indices, top_k_probs
    
    @staticmethod
    def softmax(x):
        """计算softmax"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            'model_name': self.model_config.get('model_name'),
            'export_time': self.model_config.get('export_time'),
            'best_score': self.model_config.get('best_score'),
            'vocab_size': self.dataset_info.get('vocab_size'),
            'code_size': self.dataset_info.get('code_size'),
            'block_size': self.model_args.get('block_size'),
            'n_layer': self.model_args.get('n_layer'),
            'n_head': self.model_args.get('n_head'),
            'd_model': self.model_args.get('d_model')
        }


def create_sample_data(predictor):
    """创建示例数据用于测试"""
    block_size = predictor.model_args['block_size']
    code_size = predictor.dataset_info['code_size']
    vocab_size = predictor.dataset_info['vocab_size']
    
    # 生成随机测试数据
    batch_size = 1
    code = np.random.randint(0, code_size, (batch_size, block_size))
    x = np.random.randint(0, vocab_size, (batch_size, block_size))
    
    # 时间特征维度
    time_encoding = predictor.model_args.get('time_encoding', 'timeF')
    time_dim = 8 if time_encoding == 'timeF' else 4
    x_mark = np.random.randn(batch_size, block_size, time_dim)
    
    return code, x, x_mark


def main():
    parser = ArgumentParser(description='使用ONNX格式BarGpt4模型进行推理')
    parser.add_argument('--onnx_path', type=str, required=True, help='ONNX模型文件路径')
    parser.add_argument('--config_path', type=str, required=True, help='模型配置文件路径')
    parser.add_argument('--tokenizer_path', type=str, help='Tokenizer文件路径')
    parser.add_argument('--top_k', type=int, default=5, help='返回概率最高的k个token')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.onnx_path):
        print(f"错误: ONNX模型文件不存在 {args.onnx_path}")
        return
    
    if not os.path.exists(args.config_path):
        print(f"错误: 配置文件不存在 {args.config_path}")
        return
    
    try:
        # 创建预测器
        predictor = ONNXBarGpt4Predictor(
            args.onnx_path, 
            args.config_path, 
            args.tokenizer_path
        )
        
        # 显示模型信息
        print(f"\n=== 模型信息 ===")
        model_info = predictor.get_model_info()
        for key, value in model_info.items():
            print(f"{key}: {value}")
        
        # 创建示例数据
        print(f"\n=== 创建示例数据 ===")
        code, x, x_mark = create_sample_data(predictor)
        print(f"输入形状:")
        print(f"  code: {code.shape}")
        print(f"  x: {x.shape}")
        print(f"  x_mark: {x_mark.shape}")
        
        # 进行预测
        print(f"\n=== 进行预测 ===")
        top_k_tokens, top_k_probs = predictor.predict_next_token(
            code, x, x_mark, top_k=args.top_k
        )
        
        print(f"Top-{args.top_k} 预测结果:")
        for i, (token, prob) in enumerate(zip(top_k_tokens, top_k_probs)):
            print(f"  {i+1}. Token {token}: {prob:.4f}")
        
        # 获取完整预测结果
        logits = predictor.predict(code, x, x_mark)
        print(f"\n预测logits形状: {logits.shape}")
        print(f"预测logits范围: [{logits.min():.3f}, {logits.max():.3f}]")
        
        print(f"\n✅ 推理测试完成！")
        
    except Exception as e:
        print(f"❌ 推理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
