"""
TimeSeriesModel2drV2 使用示例
展示不同配置下的模型使用方法
"""

import torch
import numpy as np
from time_series_model2dr_v2 import TimeSeriesModel2drV2


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建基础模型（与原始模型兼容）
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        dropout=0.5,
        kernel_size=3,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
        activation="relu",
        pooling="adaptive_avg",
    )
    
    # 生成示例数据
    batch_size = 8
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 前向传播
    output = model(embds, x)
    print(f"输入形状: embds={embds.shape}, x={x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"模型参数: {model.get_model_size()}")
    print()


def example_advanced_features():
    """高级特性示例"""
    print("=== 高级特性示例 ===")
    
    # 创建具有高级特性的模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72, 5, 11],
        num_channel=5,
        num_input=45,
        dropout=0.3,
        kernel_size=3,
        num_conv_layers=4,  # 更多卷积层
        conv_channels=[32, 64, 128, 256],  # 自定义通道数
        use_residual=True,  # 残差连接
        use_attention=True,  # 注意力机制
        use_temporal_conv=True,  # 时序卷积
        feature_fusion=True,  # 特征融合
        out_channels=(32, 64, 1152, 512),
        ins_nums=(0, 51, 51, 17),
        activation="gelu",
        pooling="adaptive_avg",
    )
    
    # 生成示例数据
    batch_size = 8
    embds = torch.randint(0, 10, (batch_size, 5, 3))  # 3个嵌入特征
    x = torch.randn(batch_size, 5, 45)
    
    # 前向传播
    output = model(embds, x)
    print(f"输入形状: embds={embds.shape}, x={x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"模型参数: {model.get_model_size()}")
    print()


def example_probabilistic_prediction():
    """概率预测示例"""
    print("=== 概率预测示例 ===")
    
    # 创建概率预测模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        dropout=0.3,
        probabilistic=True,  # 启用概率预测
        use_residual=True,
        use_attention=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 生成示例数据
    batch_size = 8
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 前向传播（训练模式）
    model.train()
    mean, var = model(embds, x)
    print(f"概率预测 - 均值形状: {mean.shape}, 方差形状: {var.shape}")
    print(f"均值范围: [{mean.min().item():.4f}, {mean.max().item():.4f}]")
    print(f"方差范围: [{var.min().item():.4f}, {var.max().item():.4f}]")
    
    # 不确定性估计
    model.eval()
    mean_pred, uncertainty = model.predict_with_uncertainty(embds, x, n_samples=10)
    print(f"不确定性估计 - 预测形状: {mean_pred.shape}, 不确定性形状: {uncertainty.shape}")
    print(f"平均不确定性: {uncertainty.mean().item():.4f}")
    print()


def example_multi_task_learning():
    """多任务学习示例"""
    print("=== 多任务学习示例 ===")
    
    # 创建多任务模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        dropout=0.3,
        multi_task=True,  # 启用多任务学习
        task_weights=[1.0, 0.3],  # 主任务和辅助任务权重
        use_residual=True,
        use_attention=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 生成示例数据
    batch_size = 8
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 前向传播
    main_output, aux_output = model(embds, x)
    print(f"主任务输出形状: {main_output.shape}")
    print(f"辅助任务输出形状: {aux_output.shape}")
    
    # 计算损失
    targets = torch.randn(batch_size)
    aux_targets = torch.randint(0, 3, (batch_size,))  # 方向分类目标
    
    loss, loss_dict = model.compute_loss((main_output, aux_output), targets, aux_targets)
    print(f"总损失: {loss.item():.4f}")
    print(f"损失组件: {loss_dict}")
    print()


def example_multi_output():
    """多变量输出示例"""
    print("=== 多变量输出示例 ===")
    
    # 创建多变量输出模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        dropout=0.3,
        num_outputs=3,  # 输出3个变量
        use_residual=True,
        use_attention=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 生成示例数据
    batch_size = 8
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 前向传播
    output = model(embds, x)
    print(f"多变量输出形状: {output.shape}")
    print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
    print()


def example_inference_mode():
    """推理模式示例"""
    print("=== 推理模式示例 ===")
    
    # 创建推理模式模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        dropout=0.0,  # 推理模式下自动设为0
        inference_mode=True,  # 推理模式
        use_residual=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 生成示例数据
    batch_size = 1  # 推理通常是单样本
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 推理
    model.eval()
    with torch.no_grad():
        output = model(embds, x)
    
    print(f"推理输出: {output.item():.6f}")
    
    # 导出ONNX
    onnx_path = "model_inference.onnx"
    model.export_onnx(onnx_path, embds, x)
    print(f"ONNX模型已导出到: {onnx_path}")
    print()


def example_attention_analysis():
    """注意力分析示例"""
    print("=== 注意力分析示例 ===")
    
    # 创建带注意力的模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        dropout=0.3,
        use_attention=True,
        use_residual=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 生成示例数据
    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 获取注意力权重
    attention_weights = model.get_attention_weights(embds, x)
    print(f"注意力权重: {list(attention_weights.keys())}")
    
    # 前向传播
    output = model(embds, x)
    print(f"输出形状: {output.shape}")
    print()


def example_model_comparison():
    """模型对比示例"""
    print("=== 模型对比示例 ===")
    
    # 基础模型
    basic_model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 高级模型
    advanced_model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=4,
        use_residual=True,
        use_attention=True,
        use_temporal_conv=True,
        feature_fusion=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8),
    )
    
    # 对比模型大小
    basic_info = basic_model.get_model_size()
    advanced_info = advanced_model.get_model_size()
    
    print("基础模型:")
    print(f"  参数数量: {basic_info['total_params']:,}")
    print(f"  模型大小: {basic_info['model_size_mb']:.2f} MB")
    
    print("高级模型:")
    print(f"  参数数量: {advanced_info['total_params']:,}")
    print(f"  模型大小: {advanced_info['model_size_mb']:.2f} MB")
    
    print(f"参数增长: {(advanced_info['total_params'] / basic_info['total_params'] - 1) * 100:.1f}%")
    print()


def main():
    """运行所有示例"""
    print("TimeSeriesModel2drV2 使用示例")
    print("=" * 50)
    
    example_basic_usage()
    example_advanced_features()
    example_probabilistic_prediction()
    example_multi_task_learning()
    example_multi_output()
    example_inference_mode()
    example_attention_analysis()
    example_model_comparison()
    
    print("所有示例运行完成!")


if __name__ == "__main__":
    main()
