import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional, Tuple, List

class TimeSeriesModel2drV1(nn.Module):
    """
    增强版时间序列模型，基于TimeSeriesModel2dr但添加了多项高级特性:
    1. 灵活的卷积层数
    2. 残差连接
    3. 可选的注意力机制
    4. 多变量输出支持
    5. 概率预测能力
    6. 更多正则化选项
    7. 支持训练和推理模式
    8. 支持ONNX导出

    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self,
                  num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  kernel_size=3,
                 num_conv_layers=2,  # 新参数: 卷积层数量
                 conv_channels=None,  # 新参数: 每层卷积通道数
                 use_residual=False,  # 新参数: 是否使用残差连接
                 use_attention=False,  # 新参数: 是否使用注意力机制
                 num_outputs=1,      # 新参数: 输出变量数量
                 probabilistic=False, # 新参数: 是否进行概率预测
                 weight_decay=0.0,   # 新参数: L2正则化系数
                  out_channels=(32, 64, 1152, 256),
                  ins_nums=(0,51,51,8),
                  activation="relu",
                  pooling="max",
                  inference_mode=False,  # 新参数: 是否为推理模式
                ):
        super(TimeSeriesModel2drV1, self).__init__()
        self.inference_mode = inference_mode

        if inference_mode:
            # 推理模式下禁用一些训练特性
            dropout = 0.0
            probabilistic = False
            use_attention = False  # 简化推理模型

        print(f"Model config: num_embeds={num_embeds}, num_channel={num_channel}, num_input={num_input}, "
              f"dropout={dropout}, num_conv_layers={num_conv_layers}, use_residual={use_residual}, "
              f"use_attention={use_attention}, num_outputs={num_outputs}, probabilistic={probabilistic}, "
              f"inference_mode={inference_mode}")

        assert len(ins_nums) == 4 and ins_nums[1] == ins_nums[2]

        # 确定维度和特征
        self.n_dims, n_feats = self._determine_dims(ins_nums)
        num_dims = self._calculate_num_dims(num_embeds, n_feats, ins_nums[3])

        # 设置模型配置参数
        self.use_residual = use_residual
        self.use_attention = use_attention
        self.probabilistic = probabilistic
        self.weight_decay = weight_decay
        self.num_outputs = num_outputs

        # 创建嵌入层
        self.embedding_layers = nn.ModuleList([
            nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i])
            for i in range(len(num_dims))
        ])

        # 基本层
        self.flatten = nn.Flatten()
        activation_layer = self._get_activation_layer(activation)
        pooling_layer = self._get_pooling_layer(pooling)

        # 设置卷积通道
        if conv_channels is None:
            if num_conv_layers <= 2:
                conv_channels = [out_channels[0], out_channels[1]]
        else:
                # 自动创建递增的通道数
                conv_channels = [
                    int(out_channels[0] * (1 + 0.5 * i)) for i in range(num_conv_layers)
                ]

        assert len(conv_channels) == num_conv_layers, "卷积通道数量必须与卷积层数量一致"
        self.conv_channels = conv_channels

        # 创建卷积层
        self.conv_layers = nn.ModuleList()
        in_channels = num_channel

        for i in range(num_conv_layers):
            # 卷积层
            conv_layer = self._create_conv_layer(
                in_channels,
                conv_channels[i],
                kernel_size,
                activation_layer,
                pooling_layer if i == num_conv_layers - 1 else None  # 只在最后一层使用池化
            )
            self.conv_layers.append(conv_layer)
            in_channels = conv_channels[i]

        # 注意力机制
        self.attention = None
        if use_attention:
            self.attention = self._create_attention_layer(conv_channels[-1])

        # 计算展平后的特征维度
        # 假设最后一个卷积层输出尺寸为 conv_channels[-1] x H x W
        # 这里需要根据您的具体模型结构来计算
        flat_features = out_channels[2]  # 使用现有模型的值

        # 输出层
        linear_out_features = out_channels[3]
        self.linear1 = nn.Sequential(
            nn.Linear(flat_features, linear_out_features),
            nn.BatchNorm1d(linear_out_features),
            activation_layer,
            nn.Dropout(dropout),
        )

        # 输出层 - 支持多变量和概率预测
        if probabilistic:
            # 对于概率预测，我们输出均值和方差
            self.linear2_mean = nn.Linear(linear_out_features, num_outputs)
            self.linear2_var = nn.Linear(linear_out_features, num_outputs)
            self.softplus = nn.Softplus()
        else:
            # 对于确定性预测，直接输出预测值
            self.linear2 = nn.Linear(linear_out_features, num_outputs)

    def _determine_dims(self, ins_nums):
        """确定输入数据的维度"""
        if ins_nums[0] > 0 and ins_nums[1] == 0 and ins_nums[2] == 0:
            return 2, ins_nums[0]
        elif ins_nums[0] == 0 and ins_nums[1] > 0 and ins_nums[2] > 0:
            return 3, ins_nums[1]
        else:
            raise ValueError("无效的ins_nums配置")

    def _calculate_num_dims(self, num_embeds, n_feats, ins_num_3):
        """计算嵌入维度"""
        num_dims = [math.ceil(np.sqrt(num_embed)) for num_embed in num_embeds if num_embed > 0]
        dims_sum = sum(num_dims)
        num_dims = [int(num_dim / dims_sum * (n_feats - ins_num_3)) for num_dim in num_dims]

        diff = sum(num_dims) - (n_feats - ins_num_3)
        if diff != 0:
            num_dims[0] -= diff

        return num_dims

    def _get_activation_layer(self, activation):
        """获取激活函数层"""
        activations = {
            "relu": nn.ReLU(),
            "gelu": nn.GELU(),
            "prelu": nn.PReLU(),
            "leakyrelu": nn.LeakyReLU(),
            "selu": nn.SELU(),
            "elu": nn.ELU(),
            "mish": nn.Mish()
        }
        if activation not in activations:
            raise ValueError("激活函数必须是: " + ", ".join(activations.keys()))
        return activations[activation]

    def _get_pooling_layer(self, pooling):
        """获取池化层"""
        poolings = {
            "max": nn.MaxPool2d(kernel_size=2),
            "avg": nn.AvgPool2d(kernel_size=2),
            "adaptive_max": nn.AdaptiveMaxPool2d(output_size=(1, 1)),
            "adaptive_avg": nn.AdaptiveAvgPool2d(output_size=(1, 1))
        }
        if pooling not in poolings:
            raise ValueError("池化方法必须是: " + ", ".join(poolings.keys()))
        return poolings[pooling]

    def _create_conv_layer(self, in_channels, out_channels, kernel_size, activation_layer, pooling_layer=None):
        """创建卷积层"""
        layers = [
            nn.Conv2d(in_channels=in_channels, out_channels=out_channels,
                     kernel_size=(kernel_size, kernel_size), stride=1, padding=1),
            nn.BatchNorm2d(out_channels),
            activation_layer
        ]
        if pooling_layer:
            layers.append(pooling_layer)
        return nn.Sequential(*layers)

    def _create_attention_layer(self, channels):
        """创建自注意力层"""
        return nn.Sequential(
            # 简化版的自注意力机制
            nn.Conv2d(channels, channels // 8, kernel_size=1),  # Query
            nn.Conv2d(channels, channels // 8, kernel_size=1),  # Key
            nn.Conv2d(channels, channels, kernel_size=1)        # Value
        )

    def _apply_attention(self, x):
        """应用自注意力机制"""
        batch_size, C, H, W = x.size()
        # 这是一个简化版的自注意力实现
        query = self.attention[0](x).view(batch_size, -1, H * W).permute(0, 2, 1)  # B x HW x C'
        key = self.attention[1](x).view(batch_size, -1, H * W)  # B x C' x HW
        value = self.attention[2](x).view(batch_size, -1, H * W).permute(0, 2, 1)  # B x HW x C

        # 计算注意力权重
        attention_weights = torch.bmm(query, key)  # B x HW x HW
        attention_weights = torch.softmax(attention_weights / (C ** 0.5), dim=2)

        # 应用注意力权重
        out = torch.bmm(attention_weights, value)  # B x HW x C
        out = out.permute(0, 2, 1).view(batch_size, C, H, W)

        # 残差连接
        return out + x

    def _embed_data(self, embds):
        """嵌入类别数据"""
        embedded_data = None
        n = embds.shape[-1] - len(self.embedding_layers) if embds.shape[-1] > len(self.embedding_layers) else 0
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](embds[:, :, i + n])
            embedded_data = category_data if embedded_data is None else torch.cat([embedded_data, category_data], dim=-1)
        return embedded_data

    def forward(self, embds, x):
        """前向传播
        Args:
            embds: 嵌入特征输入，形状为 (batch_size, seq_len, num_embeds)
            x: 时间序列特征输入，形状为 (batch_size, num_channel, seq_len)
        Returns:
            推理模式:
                - 单变量输出: (batch_size,)
                - 多变量输出: (batch_size, num_outputs)
            训练模式:
                - 确定性预测: 同推理模式
                - 概率预测: (mean, var) 元组，每个元素形状同推理模式
        """
        assert len(embds.shape) > 2

        # 处理嵌入数据
        if len(self.embedding_layers) > 0:
            embedded_data = self._embed_data(embds)
            x = torch.cat([x, embedded_data], dim=-1)

        # 调整输入形状为2D卷积格式
        x = x.reshape(x.shape[0], x.shape[1], self.n_dims, x.shape[2] // self.n_dims)

        # 应用卷积层（可选残差连接）
        for i, conv_layer in enumerate(self.conv_layers):
            conv_out = conv_layer(x)
            if self.use_residual and i > 0 and conv_out.shape == x.shape:
                x = conv_out + x  # 残差连接
            else:
                x = conv_out

        # 应用注意力机制（如果启用且不在推理模式）
        if self.use_attention and self.attention is not None and not self.inference_mode:
            x = self._apply_attention(x)

        # 展平并应用全连接层
        x = self.flatten(x)
        x = self.linear1(x)

        # 输出层处理
        if self.inference_mode:
            # 推理模式下只输出预测值
            x = self.linear2(x)
            return x.view(-1) if self.num_outputs == 1 else x
        else:
            # 训练模式支持概率预测
            if self.probabilistic:
                mean = self.linear2_mean(x)
                var = self.softplus(self.linear2_var(x)) + 1e-6
                return mean, var
            else:
                x = self.linear2(x)
                return x.view(-1) if self.num_outputs == 1 else x

    def export_onnx(self, save_path, example_embds, example_x):
        """导出ONNX模型
        Args:
            save_path: ONNX模型保存路径
            example_embds: 示例嵌入输入，形状为 (1, seq_len, num_embeds)
            example_x: 示例特征输入，形状为 (1, num_channel, seq_len)
        """
        # 切换到推理模式
        self.inference_mode = True
        self.eval()

        # 确保输入数据在正确的设备上
        if next(self.parameters()).is_cuda:
            example_embds = example_embds.cuda()
            example_x = example_x.cuda()

        # 设置动态轴
        dynamic_axes = {
            'embds': {0: 'batch_size'},
            'x': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }

        # 导出模型
        torch.onnx.export(
            self,
            (example_embds, example_x),
            save_path,
            input_names=['embds', 'x'],
            output_names=['output'],
            dynamic_axes=dynamic_axes,
            opset_version=11,
            do_constant_folding=True,
            verbose=False
        )
        print(f"模型已导出到: {save_path}")

        # 验证导出的模型
        import onnx
        onnx_model = onnx.load(save_path)
        onnx.checker.check_model(onnx_model)
        print("ONNX模型验证通过")

        # 恢复训练模式（如果需要）
        self.inference_mode = False
        self.train()

    @staticmethod
    def load_from_onnx(onnx_path):
        """从ONNX文件加载模型（仅用于推理）
        Args:
            onnx_path: ONNX模型文件路径
        Returns:
            onnx_model: ONNX运行时模型
        """
        import onnxruntime as ort

        # 创建推理会话
        ort_session = ort.InferenceSession(onnx_path)
        print(f"已加载ONNX模型: {onnx_path}")

        return ort_session

    def get_example_inputs(self, batch_size=1):
        """生成用于ONNX导出的示例输入
        Args:
            batch_size: 批次大小
        Returns:
            example_embds: 示例嵌入输入
            example_x: 示例特征输入
        """
        # 生成示例嵌入输入
        example_embds = torch.zeros(batch_size, self.num_channel, len(self.embedding_layers), dtype=torch.int64)

        # 生成示例特征输入
        example_x = torch.zeros(batch_size, self.num_channel, self.num_input, dtype=torch.float32)

        return example_embds, example_x

"""
使用示例：

# 基本使用（与原始模型相同的配置）
model = TimeSeriesModel2drV1(
    num_embeds=[72],
    num_channel=5,
    num_input=45,
    dropout=0.5,
    kernel_size=3,
    out_channels=(32, 64, 1152, 256),
    ins_nums=(0,51,51,8),
    activation="relu",
    pooling="max",
)

# 使用残差连接和更多卷积层
model_resnet = TimeSeriesModel2drV1(
    num_embeds=[72],
    num_channel=5,
    num_input=45,
    dropout=0.5,
    kernel_size=3,
    num_conv_layers=4,  # 使用4个卷积层
    conv_channels=[32, 64, 128, 256],  # 每层的通道数
    use_residual=True,  # 启用残差连接
    out_channels=(32, 64, 1152, 256),
    ins_nums=(0,51,51,8),
    activation="gelu",  # 使用GELU激活函数
    pooling="max",
)

# 使用注意力机制和多变量输出
model_attention = TimeSeriesModel2drV1(
    num_embeds=[72],
    num_channel=5,
    num_input=45,
    dropout=0.6,
    kernel_size=3,
    use_attention=True,  # 启用注意力机制
    num_outputs=3,       # 输出3个变量
    out_channels=(32, 64, 1152, 256),
    ins_nums=(0,51,51,8),
    activation="relu",
    pooling="max",
)

# 使用概率预测（输出分布而非点估计）
model_prob = TimeSeriesModel2drV1(
    num_embeds=[72],
    num_channel=5,
    num_input=45,
    dropout=0.5,
    kernel_size=3,
    probabilistic=True,  # 启用概率预测
    weight_decay=1e-5,   # 使用L2正则化
    out_channels=(32, 64, 1152, 256),
    ins_nums=(0,51,51,8),
    activation="relu",
    pooling="max",
)

# 完整训练示例（确定性模型）
def train_deterministic_model(model, train_loader, val_loader, epochs=10, lr=0.001):
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for embds, x, y in train_loader:
            optimizer.zero_grad()
            outputs = model(embds, x)
            loss = criterion(outputs, y)

            # 添加正则化损失（如果启用）
            reg_loss = model.get_regularization_loss()
            loss += reg_loss

            loss.backward()
            optimizer.step()
            train_loss += loss.item()

        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for embds, x, y in val_loader:
                outputs = model(embds, x)
                loss = criterion(outputs, y)
                val_loss += loss.item()

        print(f'Epoch {epoch+1}, Train Loss: {train_loss/len(train_loader):.6f}, Val Loss: {val_loss/len(val_loader):.6f}')

# 概率模型训练示例
def train_probabilistic_model(model, train_loader, val_loader, epochs=10, lr=0.001):
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)

    def gaussian_nll_loss(mean, var, y):
        # 高斯负对数似然损失
        return 0.5 * (torch.log(var) + ((y - mean) ** 2) / var).mean()

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for embds, x, y in train_loader:
            optimizer.zero_grad()
            mean, var = model(embds, x)
            loss = gaussian_nll_loss(mean, var, y)

            # 添加正则化损失
            reg_loss = model.get_regularization_loss()
            loss += reg_loss

            loss.backward()
            optimizer.step()
            train_loss += loss.item()

        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for embds, x, y in val_loader:
                mean, var = model(embds, x)
                loss = gaussian_nll_loss(mean, var, y)
                val_loss += loss.item()

        print(f'Epoch {epoch+1}, Train Loss: {train_loss/len(train_loader):.6f}, Val Loss: {val_loss/len(val_loader):.6f}')

# 使用不确定性估计进行预测
def predict_with_uncertainty(model, embds, x):
    mean_pred, uncertainty = model.predict_with_uncertainty(embds, x, n_samples=20)
    return mean_pred, uncertainty
"""
