{"model_configs": {"basic": {"description": "基础配置，与原始模型兼容", "num_embeds": [72], "num_channel": 5, "num_input": 45, "dropout": 0.5, "kernel_size": 3, "num_conv_layers": 2, "conv_channels": null, "use_residual": false, "use_attention": false, "use_temporal_conv": false, "feature_fusion": false, "probabilistic": false, "multi_task": false, "out_channels": [32, 64, 1152, 256], "ins_nums": [0, 51, 51, 8], "activation": "relu", "pooling": "adaptive_avg"}, "enhanced": {"description": "增强配置，启用部分高级特性", "num_embeds": [72, 5, 11], "num_channel": 5, "num_input": 45, "dropout": 0.3, "kernel_size": 3, "num_conv_layers": 3, "conv_channels": [32, 64, 128], "use_residual": true, "use_attention": true, "use_temporal_conv": false, "feature_fusion": true, "probabilistic": false, "multi_task": false, "out_channels": [32, 64, 1152, 512], "ins_nums": [0, 51, 51, 17], "activation": "gelu", "pooling": "adaptive_avg"}, "advanced": {"description": "高级配置，启用所有特性", "num_embeds": [72, 5, 11], "num_channel": 5, "num_input": 45, "dropout": 0.3, "kernel_size": 3, "num_conv_layers": 4, "conv_channels": [32, 64, 128, 256], "use_residual": true, "use_attention": true, "use_temporal_conv": true, "feature_fusion": true, "probabilistic": true, "multi_task": true, "task_weights": [1.0, 0.3], "out_channels": [32, 64, 1152, 512], "ins_nums": [0, 51, 51, 17], "activation": "gelu", "pooling": "adaptive_avg", "label_smoothing": 0.1}, "large": {"description": "大型模型配置，适用于大数据集", "num_embeds": [72, 5, 11], "num_channel": 15, "num_input": 51, "dropout": 0.2, "kernel_size": 3, "num_conv_layers": 5, "conv_channels": [64, 128, 256, 512, 1024], "use_residual": true, "use_attention": true, "use_temporal_conv": true, "feature_fusion": true, "probabilistic": true, "multi_task": true, "task_weights": [1.0, 0.2], "out_channels": [64, 128, 2048, 1024], "ins_nums": [0, 51, 51, 17], "activation": "gelu", "pooling": "adaptive_avg", "label_smoothing": 0.05}, "inference": {"description": "推理优化配置", "num_embeds": [72], "num_channel": 5, "num_input": 45, "dropout": 0.0, "kernel_size": 3, "num_conv_layers": 3, "conv_channels": [32, 64, 128], "use_residual": true, "use_attention": false, "use_temporal_conv": false, "feature_fusion": false, "probabilistic": false, "multi_task": false, "inference_mode": true, "out_channels": [32, 64, 1152, 256], "ins_nums": [0, 51, 51, 8], "activation": "gelu", "pooling": "adaptive_avg"}}, "training_configs": {"default": {"description": "默认训练配置", "epochs": 100, "batch_size": 64, "learning_rate": 0.001, "weight_decay": 0.0001, "optimizer": "adamw", "scheduler": "cosine", "grad_clip": 1.0, "patience": 15, "min_delta": 0.001}, "fast": {"description": "快速训练配置", "epochs": 50, "batch_size": 128, "learning_rate": 0.002, "weight_decay": 0.0001, "optimizer": "adamw", "scheduler": "step", "step_size": 20, "gamma": 0.5, "grad_clip": 1.0, "patience": 10, "min_delta": 0.001}, "careful": {"description": "谨慎训练配置，防止过拟合", "epochs": 200, "batch_size": 32, "learning_rate": 0.0005, "weight_decay": 0.001, "optimizer": "adamw", "scheduler": "plateau", "factor": 0.5, "scheduler_patience": 8, "grad_clip": 0.5, "patience": 25, "min_delta": 0.0005}, "large_data": {"description": "大数据集训练配置", "epochs": 150, "batch_size": 256, "learning_rate": 0.003, "weight_decay": 1e-05, "optimizer": "adamw", "scheduler": "cosine", "grad_clip": 2.0, "patience": 20, "min_delta": 0.0001}}, "data_configs": {"futures_15min": {"description": "期货15分钟数据配置", "ds_name": "15HF", "ds_files": ["main.2023", "main.2024"], "data_path": "f:/featdata/main", "fut_codes": "MAIN_SEL_FUT_CODES", "num_channel": 15, "seq_len": 30, "pred_len": 1, "embed_time": "timeF"}, "futures_5min": {"description": "期货5分钟数据配置", "ds_name": "5HF", "ds_files": ["main.2023", "main.2024"], "data_path": "f:/featdata/main", "fut_codes": "MAIN_SEL_FUT_CODES", "num_channel": 5, "seq_len": 60, "pred_len": 1, "embed_time": "timeF"}, "stocks_daily": {"description": "股票日线数据配置", "ds_name": "1D", "ds_files": ["stock.2023", "stock.2024"], "data_path": "f:/featdata/stock", "fut_codes": "STOCK_CODES", "num_channel": 30, "seq_len": 20, "pred_len": 1, "embed_time": "fixed"}}, "experiment_configs": {"ablation_study": {"description": "消融实验配置", "experiments": [{"name": "baseline", "model_config": "basic", "training_config": "default"}, {"name": "with_residual", "model_config": "basic", "model_overrides": {"use_residual": true}, "training_config": "default"}, {"name": "with_attention", "model_config": "basic", "model_overrides": {"use_attention": true}, "training_config": "default"}, {"name": "with_temporal_conv", "model_config": "basic", "model_overrides": {"use_temporal_conv": true}, "training_config": "default"}, {"name": "full_features", "model_config": "advanced", "training_config": "default"}]}, "hyperparameter_search": {"description": "超参数搜索配置", "search_space": {"learning_rate": [0.0001, 0.0005, 0.001, 0.002, 0.005], "dropout": [0.1, 0.2, 0.3, 0.4, 0.5], "num_conv_layers": [2, 3, 4, 5], "weight_decay": [1e-05, 0.0001, 0.001]}, "search_method": "random", "n_trials": 50, "metric": "val_loss"}}, "deployment_configs": {"production": {"description": "生产环境配置", "model_config": "inference", "onnx_export": true, "quantization": false, "batch_size": 1, "device": "cpu"}, "batch_inference": {"description": "批量推理配置", "model_config": "enhanced", "model_overrides": {"inference_mode": true}, "onnx_export": true, "batch_size": 256, "device": "cuda"}}}