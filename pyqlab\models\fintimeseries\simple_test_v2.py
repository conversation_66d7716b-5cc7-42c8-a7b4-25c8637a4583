"""
简化的TimeSeriesModel2drV2测试
验证基本功能是否正常工作
"""

import torch
import numpy as np
from time_series_model2dr_v2 import TimeSeriesModel2drV2


def test_basic_model():
    """测试基础模型"""
    print("=== 测试基础模型 ===")
    
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )
    
    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    model.eval()
    with torch.no_grad():
        output = model(embds, x)
    
    print(f"✓ 基础模型测试通过")
    print(f"  输入: embds={embds.shape}, x={x.shape}")
    print(f"  输出: {output.shape}")
    print(f"  参数数量: {model.get_model_size()['total_params']:,}")
    print()


def test_advanced_model():
    """测试高级模型（简化版）"""
    print("=== 测试高级模型 ===")
    
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=3,
        use_residual=True,
        use_attention=True,
        use_temporal_conv=False,  # 暂时关闭时序卷积
        feature_fusion=True,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )
    
    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    model.eval()
    with torch.no_grad():
        output = model(embds, x)
    
    print(f"✓ 高级模型测试通过")
    print(f"  输入: embds={embds.shape}, x={x.shape}")
    print(f"  输出: {output.shape}")
    print(f"  参数数量: {model.get_model_size()['total_params']:,}")
    print()


def test_probabilistic_model():
    """测试概率预测模型"""
    print("=== 测试概率预测模型 ===")
    
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        probabilistic=True,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )
    
    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    # 训练模式
    model.train()
    mean, var = model(embds, x)
    
    print(f"✓ 概率预测模型测试通过")
    print(f"  均值形状: {mean.shape}")
    print(f"  方差形状: {var.shape}")
    print(f"  均值范围: [{mean.min().item():.4f}, {mean.max().item():.4f}]")
    print(f"  方差范围: [{var.min().item():.4f}, {var.max().item():.4f}]")
    print()


def test_multi_task_model():
    """测试多任务模型"""
    print("=== 测试多任务模型 ===")
    
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        multi_task=True,
        task_weights=[1.0, 0.3],
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )
    
    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    model.eval()
    with torch.no_grad():
        main_output, aux_output = model(embds, x)
    
    print(f"✓ 多任务模型测试通过")
    print(f"  主任务输出: {main_output.shape}")
    print(f"  辅助任务输出: {aux_output.shape}")
    print()


def test_multi_output_model():
    """测试多变量输出模型"""
    print("=== 测试多变量输出模型 ===")
    
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        num_outputs=3,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )
    
    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    model.eval()
    with torch.no_grad():
        output = model(embds, x)
    
    print(f"✓ 多变量输出模型测试通过")
    print(f"  输出形状: {output.shape}")
    print()


def test_inference_model():
    """测试推理模型"""
    print("=== 测试推理模型 ===")
    
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        inference_mode=True,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )
    
    batch_size = 1
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)
    
    model.eval()
    with torch.no_grad():
        import time
        start_time = time.time()
        output = model(embds, x)
        inference_time = time.time() - start_time
    
    print(f"✓ 推理模型测试通过")
    print(f"  推理时间: {inference_time*1000:.2f}ms")
    print(f"  输出值: {output.item():.6f}")
    print()


def test_model_comparison():
    """测试模型对比"""
    print("=== 测试模型对比 ===")
    
    configs = [
        ("基础模型", {
            "num_embeds": [72],
            "num_channel": 5,
            "num_input": 45,
            "num_conv_layers": 2,
            "use_residual": False,
            "use_attention": False,
            "use_temporal_conv": False,
            "feature_fusion": False,
            "out_channels": (32, 64, 1152, 256),
            "ins_nums": (0, 51, 51, 8)
        }),
        ("增强模型", {
            "num_embeds": [72],
            "num_channel": 5,
            "num_input": 45,
            "num_conv_layers": 3,
            "use_residual": True,
            "use_attention": True,
            "use_temporal_conv": False,
            "feature_fusion": True,
            "out_channels": (32, 64, 1152, 256),
            "ins_nums": (0, 51, 51, 8)
        })
    ]
    
    for name, config in configs:
        model = TimeSeriesModel2drV2(**config)
        model_info = model.get_model_size()
        
        print(f"{name}:")
        print(f"  参数数量: {model_info['total_params']:,}")
        print(f"  模型大小: {model_info['model_size_mb']:.2f} MB")
    
    print()


def main():
    """运行所有测试"""
    print("TimeSeriesModel2drV2 简化测试套件")
    print("=" * 50)
    
    tests = [
        test_basic_model,
        test_advanced_model,
        test_probabilistic_model,
        test_multi_task_model,
        test_multi_output_model,
        test_inference_model,
        test_model_comparison
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} 失败: {e}")
            failed += 1
    
    print("=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
