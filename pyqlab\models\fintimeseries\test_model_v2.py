"""
TimeSeriesModel2drV2 测试脚本
验证模型的基本功能和性能
"""

import torch
import torch.nn as nn
import numpy as np
import time
import json
from time_series_model2dr_v2 import TimeSeriesModel2drV2


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")

    # 创建模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,  # 明确指定层数
        use_residual=False,  # 关闭高级特性
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )

    # 生成测试数据
    batch_size = 8
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)

    # 前向传播
    model.eval()
    with torch.no_grad():
        output = model(embds, x)

    # 验证输出形状
    expected_shape = (batch_size,)
    assert output.shape == expected_shape, f"输出形状错误: {output.shape} != {expected_shape}"

    print(f"✓ 基本前向传播测试通过")
    print(f"  输入形状: embds={embds.shape}, x={x.shape}")
    print(f"  输出形状: {output.shape}")
    print(f"  输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
    print()


def test_advanced_features():
    """测试高级特性"""
    print("=== 测试高级特性 ===")

    # 创建高级模型
    model = TimeSeriesModel2drV2(
        num_embeds=[72, 5, 11],
        num_channel=5,
        num_input=45,
        num_conv_layers=4,
        conv_channels=[32, 64, 128, 256],
        use_residual=True,
        use_attention=True,
        use_temporal_conv=True,
        feature_fusion=True,
        out_channels=(32, 64, 1152, 512),
        ins_nums=(0, 51, 51, 17)
    )

    # 生成测试数据
    batch_size = 4
    embds = torch.randint(0, 10, (batch_size, 5, 3))  # 3个嵌入特征
    x = torch.randn(batch_size, 5, 45)

    # 前向传播
    model.eval()
    with torch.no_grad():
        output = model(embds, x)

    assert output.shape == (batch_size,), f"输出形状错误: {output.shape}"

    print(f"✓ 高级特性测试通过")
    print(f"  模型参数: {model.get_model_size()}")
    print()


def test_probabilistic_prediction():
    """测试概率预测"""
    print("=== 测试概率预测 ===")

    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        probabilistic=True,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )

    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)

    # 训练模式下的概率预测
    model.train()
    mean, var = model(embds, x)

    assert mean.shape == (batch_size,), f"均值形状错误: {mean.shape}"
    assert var.shape == (batch_size,), f"方差形状错误: {var.shape}"
    assert torch.all(var > 0), "方差必须为正数"

    # 不确定性估计
    model.eval()
    mean_pred, uncertainty = model.predict_with_uncertainty(embds, x, n_samples=5)

    assert mean_pred.shape == (batch_size,), f"预测均值形状错误: {mean_pred.shape}"
    assert uncertainty.shape == (batch_size,), f"不确定性形状错误: {uncertainty.shape}"

    print(f"✓ 概率预测测试通过")
    print(f"  均值范围: [{mean.min().item():.4f}, {mean.max().item():.4f}]")
    print(f"  方差范围: [{var.min().item():.4f}, {var.max().item():.4f}]")
    print(f"  平均不确定性: {uncertainty.mean().item():.4f}")
    print()


def test_multi_task_learning():
    """测试多任务学习"""
    print("=== 测试多任务学习 ===")

    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        multi_task=True,
        task_weights=[1.0, 0.3],
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )

    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)

    # 前向传播
    model.eval()
    with torch.no_grad():
        main_output, aux_output = model(embds, x)

    assert main_output.shape == (batch_size,), f"主任务输出形状错误: {main_output.shape}"
    assert aux_output.shape == (batch_size, 3), f"辅助任务输出形状错误: {aux_output.shape}"

    # 测试损失计算
    targets = torch.randn(batch_size)
    aux_targets = torch.randint(0, 3, (batch_size,))

    loss, loss_dict = model.compute_loss((main_output, aux_output), targets, aux_targets)

    assert 'main_loss' in loss_dict, "缺少主任务损失"
    assert 'aux_loss' in loss_dict, "缺少辅助任务损失"
    assert 'total_loss' in loss_dict, "缺少总损失"

    print(f"✓ 多任务学习测试通过")
    print(f"  主任务输出形状: {main_output.shape}")
    print(f"  辅助任务输出形状: {aux_output.shape}")
    print(f"  损失组件: {list(loss_dict.keys())}")
    print()


def test_multi_output():
    """测试多变量输出"""
    print("=== 测试多变量输出 ===")

    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        num_outputs=3,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )

    batch_size = 4
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)

    # 前向传播
    model.eval()
    with torch.no_grad():
        output = model(embds, x)

    expected_shape = (batch_size, 3)
    assert output.shape == expected_shape, f"输出形状错误: {output.shape} != {expected_shape}"

    print(f"✓ 多变量输出测试通过")
    print(f"  输出形状: {output.shape}")
    print()


def test_inference_mode():
    """测试推理模式"""
    print("=== 测试推理模式 ===")

    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        inference_mode=True,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )

    batch_size = 1
    embds = torch.randint(0, 72, (batch_size, 5, 1))
    x = torch.randn(batch_size, 5, 45)

    # 推理
    model.eval()
    with torch.no_grad():
        start_time = time.time()
        output = model(embds, x)
        inference_time = time.time() - start_time

    assert output.shape == (batch_size,), f"推理输出形状错误: {output.shape}"

    print(f"✓ 推理模式测试通过")
    print(f"  推理时间: {inference_time*1000:.2f}ms")
    print(f"  输出值: {output.item():.6f}")
    print()


def test_onnx_export():
    """测试ONNX导出"""
    print("=== 测试ONNX导出 ===")

    model = TimeSeriesModel2drV2(
        num_embeds=[72],
        num_channel=5,
        num_input=45,
        num_conv_layers=2,
        inference_mode=True,
        use_residual=False,
        use_attention=False,
        use_temporal_conv=False,
        feature_fusion=False,
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0, 51, 51, 8)
    )

    # 生成示例输入
    example_embds, example_x = model.get_example_inputs(batch_size=1)

    # 导出ONNX
    onnx_path = "test_model_v2.onnx"
    try:
        model.export_onnx(onnx_path, example_embds, example_x)
        print(f"✓ ONNX导出测试通过")
        print(f"  导出路径: {onnx_path}")

        # 验证ONNX模型
        try:
            import onnxruntime as ort
            session = ort.InferenceSession(onnx_path)

            # 测试推理
            input_dict = {
                'embds': example_embds.numpy(),
                'x': example_x.numpy()
            }
            onnx_output = session.run(None, input_dict)[0]

            # 对比PyTorch输出
            model.eval()
            with torch.no_grad():
                torch_output = model(example_embds, example_x).numpy()

            diff = np.abs(onnx_output - torch_output).max()
            assert diff < 1e-5, f"ONNX输出与PyTorch输出差异过大: {diff}"

            print(f"  ONNX推理验证通过，最大差异: {diff:.2e}")

        except ImportError:
            print("  警告: 未安装onnxruntime，跳过ONNX推理验证")

    except Exception as e:
        print(f"  ONNX导出失败: {e}")

    print()


def test_performance():
    """测试性能"""
    print("=== 测试性能 ===")

    # 测试不同配置的性能
    configs = [
        ("基础配置", {
            "num_embeds": [72],
            "num_channel": 5,
            "num_input": 45,
            "num_conv_layers": 2,
            "use_residual": False,
            "use_attention": False,
            "use_temporal_conv": False,
            "feature_fusion": False,
            "out_channels": (32, 64, 1152, 256),
            "ins_nums": (0, 51, 51, 8)
        }),
        ("高级配置", {
            "num_embeds": [72],
            "num_channel": 5,
            "num_input": 45,
            "num_conv_layers": 4,
            "use_residual": True,
            "use_attention": True,
            "use_temporal_conv": True,
            "feature_fusion": True,
            "out_channels": (32, 64, 1152, 256),
            "ins_nums": (0, 51, 51, 8)
        })
    ]

    batch_size = 32
    num_runs = 10

    for config_name, config in configs:
        model = TimeSeriesModel2drV2(**config)
        model.eval()

        # 生成测试数据
        embds = torch.randint(0, 72, (batch_size, 5, 1))
        x = torch.randn(batch_size, 5, 45)

        # 预热
        with torch.no_grad():
            _ = model(embds, x)

        # 性能测试
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = model(embds, x)
                times.append(time.time() - start_time)

        avg_time = np.mean(times)
        std_time = np.std(times)
        throughput = batch_size / avg_time

        model_info = model.get_model_size()

        print(f"{config_name}:")
        print(f"  参数数量: {model_info['total_params']:,}")
        print(f"  模型大小: {model_info['model_size_mb']:.2f} MB")
        print(f"  平均推理时间: {avg_time*1000:.2f}±{std_time*1000:.2f}ms")
        print(f"  吞吐量: {throughput:.1f} samples/sec")
        print()


def run_all_tests():
    """运行所有测试"""
    print("TimeSeriesModel2drV2 测试套件")
    print("=" * 50)

    tests = [
        test_basic_functionality,
        test_advanced_features,
        test_probabilistic_prediction,
        test_multi_task_learning,
        test_multi_output,
        test_inference_mode,
        test_onnx_export,
        test_performance
    ]

    passed = 0
    failed = 0

    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} 失败: {e}")
            failed += 1

    print("=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    run_all_tests()
