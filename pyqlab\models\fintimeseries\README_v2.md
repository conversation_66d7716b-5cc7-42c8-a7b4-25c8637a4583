# TimeSeriesModel2drV2 - 增强版多周期时序因子卷积模型

## 概述

TimeSeriesModel2drV2 是基于原始 TimeSeriesModel2dr 的大幅优化版本，专门为金融时序数据预测设计。该模型集成了多项先进的深度学习技术，显著提升了预测性能和模型鲁棒性。

## 主要优化特性

### 1. 多尺度卷积架构
- **MultiScaleConvBlock**: 同时使用1×1、3×3、5×5卷积核捕获不同时间尺度的特征
- **灵活的卷积层数**: 支持2-8层可配置的卷积层
- **自适应通道数**: 根据层数自动调整通道数或手动指定

### 2. 先进的注意力机制
- **CBAM (Convolutional Block Attention Module)**: 结合通道注意力和空间注意力
- **ChannelAttention**: 学习不同特征通道的重要性
- **SpatialAttention**: 学习空间位置的重要性
- **可解释性**: 支持提取注意力权重进行模型解释

### 3. 残差连接和特征融合
- **ResidualBlock**: 缓解梯度消失问题，支持更深的网络
- **AdaptiveFeatureFusion**: 自适应融合不同层次的特征
- **跳跃连接**: 保持信息流动，提升训练稳定性

### 4. 时序建模增强
- **TemporalConvBlock**: 专门的时序卷积块处理时间依赖
- **膨胀卷积**: 扩大感受野，捕获长期依赖
- **因果卷积**: 确保时序预测的因果性

### 5. 概率预测和不确定性估计
- **概率输出**: 输出预测均值和方差
- **Monte Carlo Dropout**: 基于dropout的不确定性估计
- **高斯负对数似然损失**: 更适合概率预测的损失函数

### 6. 多任务学习
- **主任务**: 价格/收益率预测
- **辅助任务**: 方向分类（上涨/下跌/横盘）
- **任务权重**: 可配置的任务重要性权重

### 7. 高级训练技术
- **标签平滑**: 提升模型泛化能力
- **梯度裁剪**: 防止梯度爆炸
- **学习率调度**: 支持余弦退火、阶梯衰减等
- **早停机制**: 防止过拟合

### 8. 生产就绪特性
- **ONNX导出**: 支持跨平台部署
- **推理模式**: 优化的推理性能
- **模型压缩**: 减少推理时的计算开销
- **批量处理**: 高效的批量推理

## 模型架构

```
输入 (embds, x)
    ↓
嵌入层 (Embedding)
    ↓
多尺度残差卷积块 × N
    ↓ (可选)
时序卷积块 × 2
    ↓
全局自适应池化
    ↓ (可选)
自适应特征融合
    ↓
特征变换层
    ↓
输出层 (单任务/多任务/概率)
```

## 使用方法

### 基础使用

```python
from time_series_model2dr_v2 import TimeSeriesModel2drV2

# 创建基础模型
model = TimeSeriesModel2drV2(
    num_embeds=[72],
    num_channel=5,
    num_input=45,
    out_channels=(32, 64, 1152, 256),
    ins_nums=(0, 51, 51, 8)
)

# 前向传播
embds = torch.randint(0, 72, (8, 5, 1))
x = torch.randn(8, 5, 45)
output = model(embds, x)
```

### 高级配置

```python
# 创建高级模型
model = TimeSeriesModel2drV2(
    num_embeds=[72, 5, 11],
    num_channel=5,
    num_input=45,
    num_conv_layers=4,           # 4层卷积
    conv_channels=[32, 64, 128, 256],  # 自定义通道数
    use_residual=True,           # 残差连接
    use_attention=True,          # 注意力机制
    use_temporal_conv=True,      # 时序卷积
    feature_fusion=True,         # 特征融合
    probabilistic=True,          # 概率预测
    multi_task=True,            # 多任务学习
    out_channels=(32, 64, 1152, 512),
    ins_nums=(0, 51, 51, 17)
)
```

### 训练示例

```python
from train_model_v2 import ModelTrainer

# 训练配置
config = {
    'epochs': 100,
    'learning_rate': 0.001,
    'optimizer': 'adamw',
    'scheduler': 'cosine',
    'patience': 15
}

# 创建训练器
trainer = ModelTrainer(model, train_loader, val_loader, config)

# 开始训练
history = trainer.train(config['epochs'])
```

## 性能对比

| 特性 | 原始模型 | V2优化版 | 提升 |
|------|----------|----------|------|
| 卷积层数 | 2层固定 | 2-8层可配置 | 灵活性↑ |
| 注意力机制 | 简单自注意力 | CBAM双重注意力 | 表达能力↑ |
| 残差连接 | 无 | 多层残差 | 训练稳定性↑ |
| 特征融合 | 无 | 自适应融合 | 特征利用率↑ |
| 不确定性估计 | 无 | MC Dropout | 可靠性↑ |
| 多任务学习 | 无 | 支持 | 泛化能力↑ |
| 概率预测 | 无 | 支持 | 预测质量↑ |

## 配置参数

### 核心参数
- `num_conv_layers`: 卷积层数量 (2-8)
- `conv_channels`: 每层卷积通道数
- `use_residual`: 是否使用残差连接
- `use_attention`: 是否使用注意力机制
- `use_temporal_conv`: 是否使用时序卷积
- `feature_fusion`: 是否使用特征融合

### 预测模式
- `probabilistic`: 概率预测模式
- `multi_task`: 多任务学习模式
- `num_outputs`: 输出变量数量

### 正则化
- `dropout`: Dropout比率
- `weight_decay`: L2正则化系数
- `label_smoothing`: 标签平滑系数

## 最佳实践

### 1. 模型选择
- **小数据集**: 使用基础配置，避免过拟合
- **大数据集**: 启用所有高级特性
- **实时预测**: 使用推理模式，关闭注意力机制

### 2. 超参数调优
- **学习率**: 从0.001开始，根据收敛情况调整
- **Dropout**: 0.1-0.5，根据过拟合程度调整
- **卷积层数**: 3-4层通常效果最佳

### 3. 训练策略
- **预热**: 使用较小学习率预热5-10个epoch
- **学习率调度**: 推荐余弦退火调度
- **早停**: 设置patience=10-20防止过拟合

### 4. 数据预处理
- **标准化**: 确保输入数据已标准化
- **特征工程**: 添加技术指标作为额外特征
- **时间编码**: 使用周期性时间特征

## 部署指南

### ONNX导出
```python
# 导出ONNX模型
model.eval()
example_embds, example_x = model.get_example_inputs()
model.export_onnx("model.onnx", example_embds, example_x)
```

### 推理优化
```python
# 创建推理优化模型
inference_model = TimeSeriesModel2drV2(
    inference_mode=True,  # 推理模式
    dropout=0.0,         # 关闭dropout
    use_attention=False  # 简化注意力
)
```

## 故障排除

### 常见问题
1. **内存不足**: 减少batch_size或卷积层数
2. **训练不收敛**: 降低学习率，增加预热epoch
3. **过拟合**: 增加dropout，启用标签平滑
4. **预测不稳定**: 启用概率预测，使用不确定性估计

### 性能调优
1. **训练速度**: 使用混合精度训练，增加num_workers
2. **推理速度**: 使用ONNX Runtime，启用推理模式
3. **内存使用**: 使用梯度累积，减少中间特征保存

## 更新日志

### v2.0.0
- 全新的多尺度卷积架构
- CBAM注意力机制
- 自适应特征融合
- 概率预测和不确定性估计
- 多任务学习支持
- 完整的训练和部署工具链

## 许可证

本项目遵循MIT许可证。
