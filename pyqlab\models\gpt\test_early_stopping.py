"""
测试早停机制是否正常工作的简单脚本
"""

import torch
import pytorch_lightning as pl
import pytorch_lightning.callbacks as plc
from pytorch_lightning import Trainer
from pytorch_lightning.loggers import TensorBoardLogger
from torch.utils.data import DataLoader, TensorDataset
import numpy as np


class SimpleTestModel(pl.LightningModule):
    """简单的测试模型，用于验证早停机制"""
    
    def __init__(self, lr=1e-3):
        super().__init__()
        self.save_hyperparameters()
        
        # 简单的线性模型
        self.linear = torch.nn.Linear(10, 1)
        self.loss_fn = torch.nn.MSELoss()
        
        # 模拟验证损失不改善的情况
        self.epoch_count = 0
        self.base_loss = 2.0
        
    def forward(self, x):
        return self.linear(x)
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = self.loss_fn(y_hat, y)
        self.log('train_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        
        # 模拟验证损失的变化模式
        self.epoch_count += 1
        
        if self.epoch_count <= 2:
            # 前两个epoch有改善
            simulated_loss = self.base_loss - 0.1 * self.epoch_count
        else:
            # 之后的epoch没有显著改善（小于min_delta）
            simulated_loss = self.base_loss - 0.2 + 0.0001 * (self.epoch_count - 2)
            
        # 使用模拟的损失值
        loss = torch.tensor(simulated_loss, requires_grad=True)
        
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        print(f"Epoch {self.epoch_count}: 模拟验证损失 = {simulated_loss:.6f}")
        
        return loss
    
    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=self.hparams.lr)


class EarlyStoppingMonitor(pl.Callback):
    """监控早停机制的回调"""
    
    def __init__(self):
        super().__init__()
        self.best_val_loss = float('inf')
        
    def on_validation_epoch_end(self, trainer, pl_module):
        current_val_loss = trainer.callback_metrics.get('val_loss')
        if current_val_loss is None:
            return
            
        current_val_loss = current_val_loss.item()
        
        # 获取早停回调
        early_stopping_callback = None
        for callback in trainer.callbacks:
            if isinstance(callback, plc.EarlyStopping):
                early_stopping_callback = callback
                break
                
        if early_stopping_callback is None:
            return
            
        min_delta = early_stopping_callback.min_delta
        patience = early_stopping_callback.patience
        
        print(f"\n=== 早停监控 (Epoch {trainer.current_epoch + 1}) ===")
        print(f"当前验证损失: {current_val_loss:.6f}")
        print(f"历史最佳损失: {self.best_val_loss:.6f}")
        print(f"min_delta设置: {min_delta:.6f}")
        print(f"patience设置: {patience}")
        
        # 检查是否有改善
        if current_val_loss < self.best_val_loss - min_delta:
            improvement = self.best_val_loss - current_val_loss
            self.best_val_loss = current_val_loss
            print(f"✅ 损失改善: {improvement:.6f}")
        else:
            print(f"⚠️  损失未改善 (需要改善至少 {min_delta:.6f})")
            
        # 显示早停回调的内部状态
        wait_count = getattr(early_stopping_callback, 'wait_count', 0)
        print(f"早停计数器: {wait_count}/{patience}")
        
        if wait_count >= patience - 1:
            print(f"🛑 下一个epoch如果仍无改善将触发早停！")
            
        print("-" * 50)


def create_dummy_data():
    """创建虚拟数据"""
    # 生成随机数据
    X = torch.randn(1000, 10)
    y = torch.randn(1000, 1)
    
    dataset = TensorDataset(X, y)
    
    # 分割训练和验证集
    train_size = 800
    val_size = 200
    
    train_dataset = TensorDataset(X[:train_size], y[:train_size])
    val_dataset = TensorDataset(X[train_size:], y[train_size:])
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    return train_loader, val_loader


def test_early_stopping(min_delta=0.001, patience=3, max_epochs=10):
    """测试早停机制"""
    print(f"\n=== 测试早停机制 ===")
    print(f"min_delta: {min_delta}")
    print(f"patience: {patience}")
    print(f"max_epochs: {max_epochs}")
    print("=" * 50)
    
    # 创建数据
    train_loader, val_loader = create_dummy_data()
    
    # 创建模型
    model = SimpleTestModel()
    
    # 创建回调
    callbacks = [
        EarlyStoppingMonitor(),
        plc.EarlyStopping(
            monitor='val_loss',
            mode='min',
            patience=patience,
            min_delta=min_delta,
            verbose=True
        ),
        plc.ModelCheckpoint(
            monitor='val_loss',
            filename='test-{epoch:02d}-{val_loss:.3f}',
            save_top_k=1,
            mode='min'
        )
    ]
    
    # 创建训练器
    trainer = Trainer(
        max_epochs=max_epochs,
        callbacks=callbacks,
        logger=False,  # 不使用logger
        enable_progress_bar=True,
        enable_checkpointing=True
    )
    
    # 训练模型
    trainer.fit(model, train_loader, val_loader)
    
    # 检查是否触发了早停
    if trainer.current_epoch + 1 < max_epochs:
        print(f"\n✅ 早停成功触发！在第 {trainer.current_epoch + 1} 个epoch停止")
        print(f"   设置的最大epoch: {max_epochs}")
        print(f"   实际训练epoch: {trainer.current_epoch + 1}")
    else:
        print(f"\n❌ 早停未触发，训练完成了所有 {max_epochs} 个epoch")
        
    return trainer.current_epoch + 1 < max_epochs


if __name__ == '__main__':
    print("开始测试早停机制...")
    
    # 测试1: min_delta=1.0 (太大，不应该触发早停)
    print("\n" + "="*60)
    print("测试1: min_delta=1.0 (太大，预期不会触发早停)")
    success1 = test_early_stopping(min_delta=1.0, patience=3, max_epochs=8)
    
    # 测试2: min_delta=0.001 (合理，应该触发早停)
    print("\n" + "="*60)
    print("测试2: min_delta=0.001 (合理，预期会触发早停)")
    success2 = test_early_stopping(min_delta=0.001, patience=3, max_epochs=8)
    
    # 总结
    print("\n" + "="*60)
    print("测试总结:")
    print(f"测试1 (min_delta=1.0): {'未触发早停' if not success1 else '触发早停'} ✅" if not success1 else f"测试1 (min_delta=1.0): {'未触发早停' if not success1 else '触发早停'} ❌")
    print(f"测试2 (min_delta=0.001): {'触发早停' if success2 else '未触发早停'} ✅" if success2 else f"测试2 (min_delta=0.001): {'触发早停' if success2 else '未触发早停'} ❌")
    
    if not success1 and success2:
        print("\n🎉 早停机制工作正常！")
        print("建议在实际训练中使用 min_delta=0.001 或更小的值")
    else:
        print("\n⚠️  早停机制可能存在问题，请检查配置")
