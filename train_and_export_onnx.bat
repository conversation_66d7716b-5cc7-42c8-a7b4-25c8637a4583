@echo off
REM 训练BarGpt4模型并导出ONNX格式
REM 这个脚本演示如何使用新的ONNX导出功能

echo ===== 使用BarTokenizer训练BarGpt4模型并导出ONNX =====
echo.

REM 设置Python环境
set PYTHONPATH=%CD%

REM 训练参数
set DATA_FILE=f:/hqdata/min1/fut_min1_2025.parquet
set DATA_PATH=f:/hqdata
set LOG_DIR=lightning_logs_onnx_export

REM 模型参数
set BLOCK_SIZE=30
set N_LAYER=4
set N_HEAD=8
set D_MODEL=128
set BATCH_SIZE=32
set MAX_EPOCHS=5
set K_FOLDS=2

REM Tokenizer参数
set MAPPING_STRATEGY=quantile
set BALANCING_STRATEGY=frequency
set N_BINS=100
set FEATURES=change,body,upper_shadow,lower_shadow,volume_ratio
set COMBINATION_METHOD=hierarchical

echo 训练配置:
echo   数据文件: %DATA_FILE%
echo   序列长度: %BLOCK_SIZE%
echo   模型层数: %N_LAYER%
echo   注意力头数: %N_HEAD%
echo   模型维度: %D_MODEL%
echo   批次大小: %BATCH_SIZE%
echo   最大轮数: %MAX_EPOCHS%
echo   交叉验证: %K_FOLDS%折
echo   映射策略: %MAPPING_STRATEGY%
echo   平衡策略: %BALANCING_STRATEGY%
echo   Token数量: %N_BINS%
echo   特征组合: %COMBINATION_METHOD%
echo.

REM 检查数据文件是否存在
if not exist "%DATA_FILE%" (
    echo 错误: 数据文件不存在 %DATA_FILE%
    echo 请检查数据路径是否正确
    pause
    exit /b 1
)

echo 开始训练...
echo.

REM 执行训练并导出ONNX
python pyqlab/models/gpt/train_bar_gpt4_with_tokenizer.py ^
    --data_file "%DATA_FILE%" ^
    --data_path "%DATA_PATH%" ^
    --start_year 2025 ^
    --end_year 2025 ^
    --block_size %BLOCK_SIZE% ^
    --mapping_strategy %MAPPING_STRATEGY% ^
    --balancing_strategy %BALANCING_STRATEGY% ^
    --n_bins %N_BINS% ^
    --features "%FEATURES%" ^
    --combination_method %COMBINATION_METHOD% ^
    --max_token_frequency 0.1 ^
    --gini_threshold 0.7 ^
    --n_layer %N_LAYER% ^
    --n_head %N_HEAD% ^
    --d_model %D_MODEL% ^
    --time_encoding timeF ^
    --time_embed_type time_feature ^
    --freq t ^
    --pos_embed_type rope ^
    --dropout 0.1 ^
    --batch_size %BATCH_SIZE% ^
    --lr 1e-4 ^
    --weight_decay 0.01 ^
    --max_epochs %MAX_EPOCHS% ^
    --k_folds %K_FOLDS% ^
    --early_stop 3 ^
    --min_delta 1e-3 ^
    --num_workers 0 ^
    --seed 42 ^
    --log_dir "%LOG_DIR%" ^
    --use_class_weights ^
    --export_onnx

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===== 训练和导出完成 =====
    echo.
    echo 检查输出文件:
    echo   日志目录: %LOG_DIR%
    echo   ONNX模型: %LOG_DIR%/onnx_models/
    echo   Tokenizer: tokenizer_%MAPPING_STRATEGY%_%N_BINS%.pkl
    echo.
    
    REM 列出生成的ONNX文件
    if exist "%LOG_DIR%\onnx_models\" (
        echo 生成的ONNX文件:
        dir /b "%LOG_DIR%\onnx_models\*.onnx"
        echo.
        echo 配置文件:
        dir /b "%LOG_DIR%\onnx_models\*_config.json"
    )
    
    echo 训练和导出成功完成！
) else (
    echo.
    echo ===== 训练或导出失败 =====
    echo 错误代码: %ERRORLEVEL%
    echo 请检查错误信息并重试
)

echo.
pause
