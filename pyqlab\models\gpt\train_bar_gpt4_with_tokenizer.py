"""
使用BarTokenizer高质量tokens训练BarGpt4模型

这个脚本专门用于测试BarTokenizer生成的高质量tokens是否能够解决
样本不均衡造成的预测集中问题。

使用min1数据进行测试。
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary
from torch.utils.data import DataLoader, Subset
from sklearn.model_selection import KFold
import random
from datetime import datetime
from time import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset
from pyqlab.models.gpt.bar_gpt4 import BarGpt4
from pyqlab.utils.config import CfgNode as CN


class BarGpt4LightningModule(pl.LightningModule):
    """BarGpt4的PyTorch Lightning包装器"""

    def __init__(self, **kwargs):
        super().__init__()
        self.save_hyperparameters()

        # 创建模型
        self.model = BarGpt4(
            block_size=self.hparams.block_size,
            code_size=self.hparams.code_size,
            vocab_size=self.hparams.vocab_size,
            n_layer=self.hparams.n_layer,
            n_head=self.hparams.n_head,
            d_model=self.hparams.d_model,
            time_encoding=self.hparams.time_encoding,
            time_embed_type=self.hparams.time_embed_type,
            freq=self.hparams.freq,
            pos_embed_type=self.hparams.pos_embed_type,
            dropout=self.hparams.dropout
        )

        # 类别权重（如果提供）
        self.class_weights = None
        if hasattr(self.hparams, 'class_weights') and self.hparams.class_weights is not None:
            self.class_weights = self.hparams.class_weights

    def forward(self, code, x, x_mark, targets=None):
        return self.model(code, x, x_mark, targets, self.class_weights)

    def training_step(self, batch, batch_idx):
        code, x, x_mark, y = batch
        logits, loss = self(code, x, x_mark, y)

        # 计算准确率
        predictions = torch.argmax(logits, dim=-1)
        accuracy = (predictions == y).float().mean()

        # 记录指标
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_acc', accuracy, on_step=True, on_epoch=True, prog_bar=True)

        return loss

    def validation_step(self, batch, batch_idx):
        code, x, x_mark, y = batch
        logits, loss = self(code, x, x_mark, y)

        # 计算准确率
        predictions = torch.argmax(logits, dim=-1)
        accuracy = (predictions == y).float().mean()

        # 分析预测多样性
        unique_predictions = torch.unique(predictions).numel()
        total_vocab = self.hparams.vocab_size
        diversity_ratio = unique_predictions / total_vocab

        # 记录指标
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_acc', accuracy, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_diversity', diversity_ratio, on_step=False, on_epoch=True, prog_bar=True)

        return loss

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.lr,
            weight_decay=self.hparams.weight_decay
        )

        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=3,
            verbose=True
        )

        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }


def create_data_loaders(dataset, batch_size, num_workers, k_folds, seed):
    """创建数据加载器"""
    # 使用KFold分割数据
    kfold = KFold(n_splits=k_folds, shuffle=True, random_state=seed)

    data_loaders = []
    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
        train_subset = Subset(dataset, train_idx)
        val_subset = Subset(dataset, val_idx)

        train_loader = DataLoader(
            train_subset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            persistent_workers=num_workers > 0
        )

        val_loader = DataLoader(
            val_subset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            persistent_workers=num_workers > 0
        )

        data_loaders.append((train_loader, val_loader))

    return data_loaders


def load_callbacks(args):
    """加载回调函数"""
    callbacks = []

    # 早停
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    # 模型检查点
    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    # 学习率监控
    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))

    # 进度条
    callbacks.append(plc.RichProgressBar())

    return callbacks


def analyze_predictions(model, val_loader, vocab_size, device):
    """分析模型预测的多样性"""
    model.eval()
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for batch in val_loader:
            code, x, x_mark, y = [tensor.to(device) for tensor in batch]
            logits, _ = model(code, x, x_mark, y)
            predictions = torch.argmax(logits, dim=-1)

            all_predictions.extend(predictions.cpu().numpy().flatten())
            all_targets.extend(y.cpu().numpy().flatten())

    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)

    # 分析预测分布
    unique_preds, pred_counts = np.unique(all_predictions, return_counts=True)
    unique_targets, target_counts = np.unique(all_targets, return_counts=True)

    print(f"\n=== 预测分析 ===")
    print(f"预测的唯一token数: {len(unique_preds)} / {vocab_size}")
    print(f"目标的唯一token数: {len(unique_targets)} / {vocab_size}")
    print(f"预测多样性比例: {len(unique_preds) / vocab_size:.2%}")

    # 检查是否存在预测集中问题
    max_pred_freq = pred_counts.max() / len(all_predictions)
    print(f"最高频预测token占比: {max_pred_freq:.2%}")

    if max_pred_freq > 0.5:
        print("⚠️  检测到预测集中问题！")
        most_common_pred = unique_preds[np.argmax(pred_counts)]
        print(f"最常预测的token: {most_common_pred}")
    else:
        print("✅ 预测分布相对均匀")

    return {
        'unique_predictions': len(unique_preds),
        'prediction_diversity': len(unique_preds) / vocab_size,
        'max_prediction_frequency': max_pred_freq
    }


def export_best_model_to_onnx(best_model_info, model_args, dataset, args):
    """导出最佳模型为ONNX格式"""
    print(f"\n=== 导出最佳模型为ONNX格式 ===")

    # 找到最佳模型
    best_fold = min(best_model_info, key=lambda x: x['best_score'])
    print(f"选择Fold {best_fold['fold'] + 1}的模型进行导出")
    print(f"最佳验证损失: {best_fold['best_score']:.4f}")

    try:
        # 加载最佳模型
        model = BarGpt4LightningModule.load_from_checkpoint(
            best_fold['model_path'],
            **model_args
        )
        model.eval()

        # 获取底层的BarGpt4模型
        bar_gpt4_model = model.model
        bar_gpt4_model.inference_mode()  # 切换到推理模式

        # 创建ONNX导出目录
        onnx_dir = os.path.join(args.log_dir, "onnx_models")
        os.makedirs(onnx_dir, exist_ok=True)

        # 生成模型文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_name = f"BarGpt4_Tokenized_{args.mapping_strategy}_{args.n_bins}bins_{timestamp}"
        onnx_path = os.path.join(onnx_dir, f"{model_name}.onnx")

        # 导出ONNX模型
        success = bar_gpt4_model.export_onnx(
            save_path=onnx_path,
            batch_size=1,  # 推理时通常使用batch_size=1
            seq_len=args.block_size,
            dynamic_axes=True,
            opset_version=13
        )

        if success:
            print(f"✅ 模型已成功导出为ONNX格式:")
            print(f"   路径: {onnx_path}")

            # 保存模型配置信息
            config_path = os.path.join(onnx_dir, f"{model_name}_config.json")
            model_config = {
                'model_name': model_name,
                'export_time': timestamp,
                'best_fold': best_fold['fold'],
                'best_score': best_fold['best_score'],
                'model_args': model_args,
                'tokenizer_config': {
                    'mapping_strategy': args.mapping_strategy,
                    'balancing_strategy': args.balancing_strategy,
                    'n_bins': args.n_bins,
                    'features': args.features.split(','),
                    'combination_method': args.combination_method,
                    'max_token_frequency': args.max_token_frequency,
                    'gini_threshold': args.gini_threshold
                },
                'dataset_info': {
                    'vocab_size': dataset.get_vocab_size(),
                    'code_size': dataset.get_code_size(),
                    'sample_count': len(dataset)
                }
            }

            import json
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(model_config, f, indent=2, ensure_ascii=False)

            print(f"   配置文件: {config_path}")

            # 验证ONNX模型
            try:
                import onnx
                onnx_model = onnx.load(onnx_path)
                onnx.checker.check_model(onnx_model)
                print(f"✅ ONNX模型验证通过")
            except Exception as e:
                print(f"⚠️  ONNX模型验证失败: {e}")

            return onnx_path, config_path
        else:
            print(f"❌ ONNX导出失败")
            return None, None

    except Exception as e:
        print(f"❌ 导出过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def main(args):
    """主训练函数"""
    print("=== 使用BarTokenizer训练BarGpt4模型 ===\n")

    # 设置随机种子
    pl.seed_everything(args.seed)

    # 创建数据集配置
    config = BarTokenizedDataset.get_default_config()
    config.data_path = args.data_path
    config.start_year = args.start_year
    config.end_year = args.end_year
    config.block_size = args.block_size
    config.timeenc = 1 if args.time_encoding == 'timeF' else 0

    # 配置tokenizer
    config.tokenizer.mapping_strategy = args.mapping_strategy
    config.tokenizer.balancing_strategy = args.balancing_strategy
    config.tokenizer.n_bins = args.n_bins
    config.tokenizer.features = args.features.split(',')
    config.tokenizer.combination_method = args.combination_method

    # 配置数据平衡
    config.balance.max_token_frequency = args.max_token_frequency
    config.balance.gini_threshold = args.gini_threshold

    print(f"数据配置: {config}")

    # 创建数据集
    print(f"正在创建数据集...")
    dataset = BarTokenizedDataset(config, args.data_file)

    print(f"数据集创建完成:")
    print(f"  样本数量: {len(dataset)}")
    print(f"  词汇表大小: {dataset.get_vocab_size()}")
    print(f"  代码数量: {dataset.get_code_size()}")

    # 获取分布统计
    stats = dataset.get_distribution_stats()
    print(f"  基尼系数: {stats.get('gini_coefficient', 'N/A')}")
    print(f"  标准化熵: {stats.get('normalized_entropy', 'N/A')}")

    # 保存tokenizer
    tokenizer_path = f"tokenizer_{args.mapping_strategy}_{args.n_bins}.pkl"
    dataset.save_tokenizer(tokenizer_path)

    # 创建数据加载器
    data_loaders = create_data_loaders(
        dataset, args.batch_size, args.num_workers, args.k_folds, args.seed
    )

    # 获取类别权重
    class_weights = dataset.get_class_weights() if args.use_class_weights else None

    # 训练参数
    model_args = {
        'block_size': args.block_size,
        'code_size': dataset.get_code_size(),
        'vocab_size': dataset.get_vocab_size(),
        'n_layer': args.n_layer,
        'n_head': args.n_head,
        'd_model': args.d_model,
        'time_encoding': args.time_encoding,
        'time_embed_type': args.time_embed_type,
        'freq': args.freq,
        'pos_embed_type': args.pos_embed_type,
        'dropout': args.dropout,
        'lr': args.lr,
        'weight_decay': args.weight_decay,
        'class_weights': class_weights
    }

    # 交叉验证训练
    best_models = []

    for fold, (train_loader, val_loader) in enumerate(data_loaders):
        print(f"\n=== 训练 Fold {fold + 1}/{args.k_folds} ===")

        # 创建模型
        model = BarGpt4LightningModule(**model_args)

        # 创建训练器
        trainer_name = f"BarGpt4_Tokenized_Fold{fold}"
        logger = TensorBoardLogger(save_dir=args.log_dir, name=trainer_name)
        callbacks = load_callbacks(args)

        trainer = Trainer(
            accelerator='auto',
            devices='auto',
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
            enable_progress_bar=True
        )

        # 训练模型
        trainer.fit(model, train_loader, val_loader)

        # 分析预测
        device = next(model.parameters()).device
        pred_analysis = analyze_predictions(
            model, val_loader, dataset.get_vocab_size(), device
        )

        # 保存最佳模型信息
        best_models.append({
            'fold': fold,
            'model_path': callbacks[1].best_model_path,
            'best_score': callbacks[1].best_model_score.item(),
            'prediction_analysis': pred_analysis
        })

        print(f"Fold {fold + 1} 完成，最佳验证损失: {callbacks[1].best_model_score:.4f}")

    # 总结结果
    print(f"\n=== 训练总结 ===")
    avg_score = np.mean([model['best_score'] for model in best_models])
    avg_diversity = np.mean([model['prediction_analysis']['prediction_diversity'] for model in best_models])
    avg_max_freq = np.mean([model['prediction_analysis']['max_prediction_frequency'] for model in best_models])

    print(f"平均验证损失: {avg_score:.4f}")
    print(f"平均预测多样性: {avg_diversity:.2%}")
    print(f"平均最高频预测占比: {avg_max_freq:.2%}")

    # 检查是否解决了预测集中问题
    if avg_max_freq < 0.3 and avg_diversity > 0.1:
        print("✅ 成功解决预测集中问题！")
    else:
        print("⚠️  预测集中问题仍然存在")

    print(f"\nTokenizer已保存到: {tokenizer_path}")

    # 导出最佳模型为ONNX格式
    if args.export_onnx:
        onnx_path, config_path = export_best_model_to_onnx(best_models, model_args, dataset, args)
        if onnx_path:
            print(f"\n✅ 最佳模型已导出为ONNX格式:")
            print(f"   模型文件: {onnx_path}")
            if config_path:
                print(f"   配置文件: {config_path}")
        else:
            print(f"\n❌ ONNX导出失败")

    print("训练完成！")


if __name__ == '__main__':
    parser = ArgumentParser(description='使用BarTokenizer训练BarGpt4模型')

    # 数据参数
    parser.add_argument('--data_file', type=str, required=True, help='数据文件路径（parquet格式）')
    parser.add_argument('--data_path', type=str, default='f:/hqdata', help='数据根目录')
    parser.add_argument('--start_year', type=int, default=2025, help='开始年份')
    parser.add_argument('--end_year', type=int, default=2025, help='结束年份')
    parser.add_argument('--block_size', type=int, default=30, help='序列长度')

    # BarTokenizer参数
    parser.add_argument('--mapping_strategy', type=str, default='quantile',
                       choices=['linear', 'quantile', 'adaptive'], help='映射策略')
    parser.add_argument('--balancing_strategy', type=str, default='frequency',
                       choices=['frequency', 'none'], help='平衡策略')
    parser.add_argument('--n_bins', type=int, default=100, help='token数量')
    parser.add_argument('--max_token_frequency', type=float, default=0.1, help='单个token最大频率')
    parser.add_argument('--gini_threshold', type=float, default=0.7, help='基尼系数阈值')
    parser.add_argument('--features', type=str, default='change, body, upper_shadow, lower_shadow, volume_ratio', help='用于分箱的特征')
    parser.add_argument('--combination_method', type=str, default='hierarchical',
                       choices=['independent', 'hash', 'hierarchical', 'enhanced'], help='特征组合方法')

    # 模型参数
    parser.add_argument('--n_layer', type=int, default=4, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--time_encoding', type=str, default='timeF', help='时间编码方式')
    parser.add_argument('--time_embed_type', type=str, default='time_feature', help='时间嵌入类型')
    parser.add_argument('--freq', type=str, default='t', help='频率类型')
    parser.add_argument('--pos_embed_type', type=str, default='rope', help='位置编码类型')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout率')

    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='权重衰减')
    parser.add_argument('--max_epochs', type=int, default=10, help='最大训练轮数')
    parser.add_argument('--k_folds', type=int, default=3, help='交叉验证折数')
    parser.add_argument('--early_stop', type=int, default=5, help='早停耐心值')
    parser.add_argument('--min_delta', type=float, default=1e-3, help='早停最小改进')

    # 其他参数
    parser.add_argument('--num_workers', type=int, default=0, help='数据加载器工作进程数')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--log_dir', type=str, default='lightning_logs_tokenized', help='日志目录')
    parser.add_argument('--use_class_weights', action='store_true', help='是否使用类别权重')
    parser.add_argument('--export_onnx', action='store_true', help='是否导出最佳模型为ONNX格式')

    args = parser.parse_args()
    main(args)
