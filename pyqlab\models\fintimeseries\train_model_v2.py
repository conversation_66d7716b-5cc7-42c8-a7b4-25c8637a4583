"""
TimeSeriesModel2drV2 训练脚本
展示如何使用优化后的模型进行训练和评估
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import argparse
import os
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from time_series_model2dr_v2 import TimeSeriesModel2drV2
from pyqlab.data.data_api import get_dataset
from pyqlab.const import *


class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=10, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None

    def __call__(self, val_loss, model):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1

        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights:
                model.load_state_dict(self.best_weights)
            return True
        return False


class ModelTrainer:
    """模型训练器"""
    def __init__(self, model, train_loader, val_loader, config):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config

        # 优化器
        self.optimizer = self._create_optimizer()

        # 学习率调度器
        self.scheduler = self._create_scheduler()

        # 早停
        self.early_stopping = EarlyStopping(
            patience=config.get('patience', 10),
            min_delta=config.get('min_delta', 0.001)
        )

        # 训练历史
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': []
        }

    def _create_optimizer(self):
        """创建优化器"""
        optimizer_name = self.config.get('optimizer', 'adamw')
        lr = self.config.get('learning_rate', 0.001)
        weight_decay = self.config.get('weight_decay', 1e-4)

        if optimizer_name.lower() == 'adamw':
            return optim.AdamW(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_name.lower() == 'adam':
            return optim.Adam(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_name.lower() == 'sgd':
            momentum = self.config.get('momentum', 0.9)
            return optim.SGD(self.model.parameters(), lr=lr, momentum=momentum, weight_decay=weight_decay)
        else:
            raise ValueError(f"不支持的优化器: {optimizer_name}")

    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler_name = self.config.get('scheduler', 'cosine')

        if scheduler_name.lower() == 'cosine':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.get('epochs', 100)
            )
        elif scheduler_name.lower() == 'step':
            return optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=self.config.get('step_size', 30),
                gamma=self.config.get('gamma', 0.1)
            )
        elif scheduler_name.lower() == 'plateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=self.config.get('factor', 0.5),
                patience=self.config.get('scheduler_patience', 5)
            )
        else:
            return None

    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_samples = 0
        loss_components = {}

        for batch_idx, batch in enumerate(self.train_loader):
            embds, x, targets = batch

            # 移动到设备
            device = next(self.model.parameters()).device
            embds = embds.to(device)
            x = x.to(device)
            targets = targets.to(device)

            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(embds, x)

            # 计算损失
            if self.model.multi_task:
                # 多任务需要额外的目标
                aux_targets = self._generate_direction_targets(targets)
                loss, loss_dict = self.model.compute_loss(outputs, targets, aux_targets)
            else:
                loss, loss_dict = self.model.compute_loss(outputs, targets)

            # 反向传播
            loss.backward()

            # 梯度裁剪
            if self.config.get('grad_clip', 0) > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config['grad_clip'])

            self.optimizer.step()

            # 统计
            batch_size = targets.size(0)
            total_loss += loss.item() * batch_size
            total_samples += batch_size

            # 累积损失组件
            for key, value in loss_dict.items():
                if key not in loss_components:
                    loss_components[key] = 0.0
                loss_components[key] += value * batch_size

        # 计算平均损失
        avg_loss = total_loss / total_samples
        avg_loss_components = {k: v / total_samples for k, v in loss_components.items()}

        return avg_loss, avg_loss_components

    def validate_epoch(self):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        total_samples = 0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in self.val_loader:
                embds, x, targets = batch

                # 移动到设备
                device = next(self.model.parameters()).device
                embds = embds.to(device)
                x = x.to(device)
                targets = targets.to(device)

                # 前向传播
                outputs = self.model(embds, x)

                # 计算损失
                if self.model.multi_task:
                    aux_targets = self._generate_direction_targets(targets)
                    loss, _ = self.model.compute_loss(outputs, targets, aux_targets)
                    predictions = outputs[0]  # 主任务输出
                else:
                    loss, _ = self.model.compute_loss(outputs, targets)
                    predictions = outputs

                # 处理概率预测
                if isinstance(predictions, tuple):
                    predictions = predictions[0]  # 取均值

                # 统计
                batch_size = targets.size(0)
                total_loss += loss.item() * batch_size
                total_samples += batch_size

                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())

        # 计算指标
        avg_loss = total_loss / total_samples
        all_predictions = torch.cat(all_predictions).numpy()
        all_targets = torch.cat(all_targets).numpy()

        metrics = self._calculate_metrics(all_predictions, all_targets)

        return avg_loss, metrics

    def _generate_direction_targets(self, targets):
        """生成方向预测目标（用于多任务学习）"""
        # 0: 下跌, 1: 横盘, 2: 上涨
        direction_targets = torch.zeros_like(targets, dtype=torch.long)
        direction_targets[targets > 0.01] = 2  # 上涨
        direction_targets[targets < -0.01] = 0  # 下跌
        direction_targets[(targets >= -0.01) & (targets <= 0.01)] = 1  # 横盘
        return direction_targets

    def _calculate_metrics(self, predictions, targets):
        """计算评估指标"""
        mse = mean_squared_error(targets, predictions)
        mae = mean_absolute_error(targets, predictions)
        r2 = r2_score(targets, predictions)

        # 方向准确率
        pred_direction = np.sign(predictions)
        true_direction = np.sign(targets)
        direction_accuracy = np.mean(pred_direction == true_direction)

        return {
            'mse': mse,
            'mae': mae,
            'r2': r2,
            'direction_accuracy': direction_accuracy
        }

    def train(self, epochs):
        """完整训练流程"""
        print(f"开始训练，共 {epochs} 个epoch")
        print(f"模型参数: {self.model.get_model_size()}")

        best_val_loss = float('inf')

        for epoch in range(epochs):
            # 训练
            train_loss, train_loss_components = self.train_epoch()

            # 验证
            val_loss, val_metrics = self.validate_epoch()

            # 更新学习率
            if self.scheduler:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_loss)
                else:
                    self.scheduler.step()

            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['val_metrics'].append(val_metrics)

            # 打印进度
            current_lr = self.optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{epochs}")
            print(f"  训练损失: {train_loss:.6f}")
            print(f"  验证损失: {val_loss:.6f}")
            print(f"  验证指标: MSE={val_metrics['mse']:.6f}, "
                  f"MAE={val_metrics['mae']:.6f}, "
                  f"R²={val_metrics['r2']:.4f}, "
                  f"方向准确率={val_metrics['direction_accuracy']:.4f}")
            print(f"  学习率: {current_lr:.2e}")

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                self.save_checkpoint(epoch, is_best=True)

            # 早停检查
            if self.early_stopping(val_loss, self.model):
                print(f"早停触发，在第 {epoch+1} 个epoch停止训练")
                break

        print("训练完成!")
        return self.history

    def save_checkpoint(self, epoch, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'history': self.history,
            'config': self.config
        }

        # 保存路径
        save_dir = self.config.get('save_dir', './checkpoints')
        os.makedirs(save_dir, exist_ok=True)

        if is_best:
            torch.save(checkpoint, os.path.join(save_dir, 'best_model.pth'))

        torch.save(checkpoint, os.path.join(save_dir, f'checkpoint_epoch_{epoch}.pth'))


def load_dataset(args):
    """加载数据集"""
    # 处理参数
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    args.num_embeds = eval(args.num_embeds)
    args.ds_files = eval(args.ds_files)
    args.fut_codes = eval(args.fut_codes)

    if args.conv_channels is not None:
        args.conv_channels = eval(args.conv_channels)

    timeenc = 1 if args.embed_time == 'timeF' else 0
    args.seq_len = args.num_channel

    # 获取数据集
    dataset = get_dataset(
        ds_files=args.ds_files,
        ins_nums=args.ins_nums,
        is_normal=args.is_normal,
        verbose=args.verbose,
        fut_codes=args.fut_codes,
        data_path=args.data_path,
        start_time=args.start_time,
        end_time=args.end_time,
        timeenc=timeenc,
        model_type=args.model_type,
        seq_len=args.seq_len,
        pred_len=args.pred_len,
    )

    dataset.load_data()
    args.ins_nums = dataset.get_ins_nums()

    return dataset


def create_model(args):
    """创建模型"""
    model = TimeSeriesModel2drV2(
        num_embeds=args.num_embeds,
        num_channel=args.num_channel,
        num_input=args.num_input,
        dropout=args.dropout,
        kernel_size=args.kernel_size,
        num_conv_layers=args.num_conv_layers,
        conv_channels=args.conv_channels,
        use_residual=args.use_residual,
        use_attention=args.use_attention,
        use_temporal_conv=args.use_temporal_conv,
        num_outputs=args.num_outputs,
        probabilistic=args.probabilistic,
        multi_task=args.multi_task,
        weight_decay=args.weight_decay,
        out_channels=args.out_channels,
        ins_nums=args.ins_nums,
        activation=args.activation,
        pooling=args.pooling,
        inference_mode=False,
        feature_fusion=args.feature_fusion,
        label_smoothing=args.label_smoothing
    )

    return model


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练TimeSeriesModel2drV2模型')

    # 数据参数
    parser.add_argument('--ds_name', default='15HF', type=str)
    parser.add_argument('--ds_files', default='["main.2023", "main.2024"]', type=str)
    parser.add_argument('--start_time', default='', type=str)
    parser.add_argument('--end_time', default='', type=str)
    parser.add_argument('--data_path', default='f:/featdata/main', type=str)
    parser.add_argument('--fut_codes', default='MAIN_SEL_FUT_CODES', type=str)
    parser.add_argument('--is_normal', default=True, action='store_true')
    parser.add_argument('--verbose', default=False, action='store_true')
    parser.add_argument('--model_type', default=0, type=int)
    parser.add_argument('--seq_len', type=int, default=30)
    parser.add_argument('--pred_len', type=int, default=1)
    parser.add_argument('--embed_time', type=str, default='fixed')

    # 模型参数
    parser.add_argument('--num_embeds', default='[72, 5, 11]', type=str)
    parser.add_argument('--num_channel', default=15, type=int)
    parser.add_argument('--num_input', default=51, type=int)
    parser.add_argument('--dropout', default=0.3, type=float)
    parser.add_argument('--kernel_size', default=3, type=int)
    parser.add_argument('--num_conv_layers', default=3, type=int)
    parser.add_argument('--conv_channels', default=None, type=str)
    parser.add_argument('--use_residual', action='store_true', default=True)
    parser.add_argument('--use_attention', action='store_true', default=True)
    parser.add_argument('--use_temporal_conv', action='store_true', default=True)
    parser.add_argument('--num_outputs', default=1, type=int)
    parser.add_argument('--probabilistic', action='store_true', default=False)
    parser.add_argument('--multi_task', action='store_true', default=False)
    parser.add_argument('--feature_fusion', action='store_true', default=True)
    parser.add_argument('--label_smoothing', default=0.0, type=float)
    parser.add_argument('--out_channels', default='(32, 64, 1600, 512)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)
    parser.add_argument('--activation', default='gelu', type=str)
    parser.add_argument('--pooling', default='adaptive_avg', type=str)

    # 训练参数
    parser.add_argument('--epochs', default=100, type=int)
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--learning_rate', default=0.001, type=float)
    parser.add_argument('--weight_decay', default=1e-4, type=float)
    parser.add_argument('--optimizer', default='adamw', type=str)
    parser.add_argument('--scheduler', default='cosine', type=str)
    parser.add_argument('--grad_clip', default=1.0, type=float)
    parser.add_argument('--patience', default=15, type=int)
    parser.add_argument('--min_delta', default=0.001, type=float)

    # 其他参数
    parser.add_argument('--device', default='cuda', type=str)
    parser.add_argument('--save_dir', default='./model_v2_checkpoints', type=str)
    parser.add_argument('--seed', default=42, type=int)
    parser.add_argument('--num_workers', default=4, type=int)

    args = parser.parse_args()

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 加载数据
    print("加载数据集...")
    dataset = load_dataset(args)

    # 数据分割
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers,
        pin_memory=True if device.type == 'cuda' else False
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True if device.type == 'cuda' else False
    )

    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")

    # 创建模型
    print("创建模型...")
    model = create_model(args)
    model = model.to(device)

    # 打印模型信息
    model_info = model.get_model_size()
    print(f"模型参数总数: {model_info['total_params']:,}")
    print(f"可训练参数: {model_info['trainable_params']:,}")
    print(f"模型大小: {model_info['model_size_mb']:.2f} MB")

    # 训练配置
    train_config = {
        'epochs': args.epochs,
        'learning_rate': args.learning_rate,
        'weight_decay': args.weight_decay,
        'optimizer': args.optimizer,
        'scheduler': args.scheduler,
        'grad_clip': args.grad_clip,
        'patience': args.patience,
        'min_delta': args.min_delta,
        'save_dir': args.save_dir
    }

    # 创建训练器
    trainer = ModelTrainer(model, train_loader, val_loader, train_config)

    # 开始训练
    history = trainer.train(args.epochs)

    # 保存训练历史
    history_path = os.path.join(args.save_dir, 'training_history.json')
    with open(history_path, 'w') as f:
        # 转换numpy数组为列表以便JSON序列化
        serializable_history = {}
        for key, value in history.items():
            if isinstance(value, list) and len(value) > 0:
                if isinstance(value[0], dict):
                    serializable_history[key] = value
                else:
                    serializable_history[key] = [float(v) for v in value]
            else:
                serializable_history[key] = value
        json.dump(serializable_history, f, indent=2)

    # 绘制训练曲线
    plot_training_history(history, args.save_dir)

    # 导出ONNX模型
    print("导出ONNX模型...")
    model.eval()
    example_embds, example_x = model.get_example_inputs(batch_size=1)
    example_embds = example_embds.to(device)
    example_x = example_x.to(device)

    onnx_path = os.path.join(args.save_dir, 'model_v2.onnx')
    model.export_onnx(onnx_path, example_embds, example_x)

    print(f"训练完成! 模型保存在: {args.save_dir}")


def plot_training_history(history, save_dir):
    """绘制训练历史"""
    plt.figure(figsize=(15, 10))

    # 损失曲线
    plt.subplot(2, 3, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title('损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # 提取验证指标
    if history['val_metrics']:
        epochs = range(len(history['val_metrics']))

        # MSE
        plt.subplot(2, 3, 2)
        mse_values = [m['mse'] for m in history['val_metrics']]
        plt.plot(epochs, mse_values)
        plt.title('验证MSE')
        plt.xlabel('Epoch')
        plt.ylabel('MSE')
        plt.grid(True)

        # MAE
        plt.subplot(2, 3, 3)
        mae_values = [m['mae'] for m in history['val_metrics']]
        plt.plot(epochs, mae_values)
        plt.title('验证MAE')
        plt.xlabel('Epoch')
        plt.ylabel('MAE')
        plt.grid(True)

        # R²
        plt.subplot(2, 3, 4)
        r2_values = [m['r2'] for m in history['val_metrics']]
        plt.plot(epochs, r2_values)
        plt.title('验证R²')
        plt.xlabel('Epoch')
        plt.ylabel('R²')
        plt.grid(True)

        # 方向准确率
        plt.subplot(2, 3, 5)
        direction_acc = [m['direction_accuracy'] for m in history['val_metrics']]
        plt.plot(epochs, direction_acc)
        plt.title('方向准确率')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_history.png'), dpi=300, bbox_inches='tight')
    plt.close()


if __name__ == '__main__':
    main()
